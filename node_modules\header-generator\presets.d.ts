export declare const MODER<PERSON>_DESKTOP: {
    browserListQuery: string;
};
export declare const MODERN_MOBILE: {
    devices: string[];
    browserListQuery: string;
};
export declare const MODERN_LINUX: {
    operatingSystems: string[];
    browserListQuery: string;
};
export declare const MODERN_LINUX_FIREFOX: {
    browserListQuery: string;
    operatingSystems: string[];
};
export declare const MODERN_LINUX_CHROME: {
    browserListQuery: string;
    operatingSystems: string[];
};
export declare const MODERN_WINDOWS: {
    operatingSystems: string[];
    browserListQuery: string;
};
export declare const MODERN_WINDOWS_FIREFOX: {
    browserListQuery: string;
    operatingSystems: string[];
};
export declare const MODERN_WINDOWS_CHROME: {
    browserListQuery: string;
    operatingSystems: string[];
};
export declare const MODERN_MACOS: {
    operatingSystems: string[];
    browserListQuery: string;
};
export declare const MODERN_MACOS_FIREFOX: {
    browserListQuery: string;
    operatingSystems: string[];
};
export declare const MODERN_MACOS_CHROME: {
    browserListQuery: string;
    operatingSystems: string[];
};
export declare const MODERN_ANDROID: {
    operatingSystems: string[];
    devices: string[];
    browserListQuery: string;
};
//# sourceMappingURL=presets.d.ts.map