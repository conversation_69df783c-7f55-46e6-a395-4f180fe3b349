# CHANGELOG

## [6.9.16](https://github.com/nodemailer/nodemailer/compare/v6.9.15...v6.9.16) (2024-10-28)


### Bug Fixes

* **addressparser:** Correctly detect if user local part is attached to domain part ([f2096c5](https://github.com/nodemailer/nodemailer/commit/f2096c51b92a69ecfbcc15884c28cb2c2f00b826))

## [6.9.15](https://github.com/nodemailer/nodemailer/compare/v6.9.14...v6.9.15) (2024-08-08)


### Bug Fixes

* Fix memory leak ([#1667](https://github.com/nodemailer/nodemailer/issues/1667)) ([baa28f6](https://github.com/nodemailer/nodemailer/commit/baa28f659641a4bc30360633673d851618f8e8bd))
* **mime:** Added GeoJSON closes [#1637](https://github.com/nodemailer/nodemailer/issues/1637) ([#1665](https://github.com/nodemailer/nodemailer/issues/1665)) ([79b8293](https://github.com/nodemailer/nodemailer/commit/79b8293ad557d36f066b4675e649dd80362fd45b))

## [6.9.14](https://github.com/nodemailer/nodemailer/compare/v6.9.13...v6.9.14) (2024-06-19)


### Bug Fixes

* **api:** Added support for Ethereal authentication ([56b2205](https://github.com/nodemailer/nodemailer/commit/56b22052a98de9e363f6c4d26d1512925349c3f3))
* **services.json:** Add Email Services Provider Feishu Mail (CN) ([#1648](https://github.com/nodemailer/nodemailer/issues/1648)) ([e9e9ecc](https://github.com/nodemailer/nodemailer/commit/e9e9ecc99b352948a912868c7912b280a05178c6))
* **services.json:** update Mailtrap host and port in well known ([#1652](https://github.com/nodemailer/nodemailer/issues/1652)) ([fc2c9ea](https://github.com/nodemailer/nodemailer/commit/fc2c9ea0b4c4f4e514143d2a138c9a23095fc827))
* **well-known-services:** Add Loopia in well known services ([#1655](https://github.com/nodemailer/nodemailer/issues/1655)) ([21a28a1](https://github.com/nodemailer/nodemailer/commit/21a28a18fc9fdf8e0e86ddd846e54641395b2cb6))

## [6.9.13](https://github.com/nodemailer/nodemailer/compare/v6.9.12...v6.9.13) (2024-03-20)


### Bug Fixes

* **tls:** Ensure servername for SMTP ([d66fdd3](https://github.com/nodemailer/nodemailer/commit/d66fdd3dccacc4bc79d697fe9009204cc8d4bde0))

## [6.9.12](https://github.com/nodemailer/nodemailer/compare/v6.9.11...v6.9.12) (2024-03-08)


### Bug Fixes

* **message-generation:** Escape single quote in address names ([4ae5fad](https://github.com/nodemailer/nodemailer/commit/4ae5fadeaac70ba91abf529fcaae65f829a39101))

## [6.9.11](https://github.com/nodemailer/nodemailer/compare/v6.9.10...v6.9.11) (2024-02-29)


### Bug Fixes

* **headers:** Ensure that Content-type is the bottom header ([c7cf97e](https://github.com/nodemailer/nodemailer/commit/c7cf97e5ecc83f8eee773359951df995c9945446))

## [6.9.10](https://github.com/nodemailer/nodemailer/compare/v6.9.9...v6.9.10) (2024-02-22)


### Bug Fixes

* **data-uri:** Do not use regular expressions for parsing data URI schemes ([12e65e9](https://github.com/nodemailer/nodemailer/commit/12e65e975d80efe6bafe6de4590829b3b5ebb492))
* **data-uri:** Moved all data-uri regexes to use the non-regex parseDataUri method ([edd5dfe](https://github.com/nodemailer/nodemailer/commit/edd5dfe5ce9b725f8b8ae2830797f65b2a2b0a33))

## [6.9.9](https://github.com/nodemailer/nodemailer/compare/v6.9.8...v6.9.9) (2024-02-01)


### Bug Fixes

* **security:** Fix issues described in GHSA-9h6g-pr28-7cqp. Do not use eternal matching pattern if only a few occurences are expected ([dd8f5e8](https://github.com/nodemailer/nodemailer/commit/dd8f5e8a4ddc99992e31df76bcff9c590035cd4a))
* **tests:** Use native node test runner, added code coverage support, removed grunt ([#1604](https://github.com/nodemailer/nodemailer/issues/1604)) ([be45c1b](https://github.com/nodemailer/nodemailer/commit/be45c1b299d012358d69247019391a02734d70af))

## [6.9.8](https://github.com/nodemailer/nodemailer/compare/v6.9.7...v6.9.8) (2023-12-30)


### Bug Fixes

* **punycode:** do not use native punycode module ([b4d0e0c](https://github.com/nodemailer/nodemailer/commit/b4d0e0c7cc4b15bc4d9e287f91d1bcaca87508b0))

## [6.9.7](https://github.com/nodemailer/nodemailer/compare/v6.9.6...v6.9.7) (2023-10-22)


### Bug Fixes

* **customAuth:** Do not require user and pass to be set for custom authentication schemes (fixes [#1584](https://github.com/nodemailer/nodemailer/issues/1584)) ([41d482c](https://github.com/nodemailer/nodemailer/commit/41d482c3f01e26111b06f3e46351b193db3fb5cb))

## [6.9.6](https://github.com/nodemailer/nodemailer/compare/v6.9.5...v6.9.6) (2023-10-09)


### Bug Fixes

* **inline:** Use 'inline' as the default Content Dispostion value for embedded images ([db32c93](https://github.com/nodemailer/nodemailer/commit/db32c93fefee527bcc239f13056e5d9181a4d8af))
* **tests:** Removed Node v12 from test matrix as it is not compatible with the test framework anymore ([7fe0a60](https://github.com/nodemailer/nodemailer/commit/7fe0a608ed6bcb70dc6b2de543ebfc3a30abf984))

## [6.9.5](https://github.com/nodemailer/nodemailer/compare/v6.9.4...v6.9.5) (2023-09-06)


### Bug Fixes

* **license:** Updated license year ([da4744e](https://github.com/nodemailer/nodemailer/commit/da4744e491f3a68f4f68e4073684370592630e01))

## 6.9.4 2023-07-19

-   Renamed SendinBlue to Brevo

## 6.9.3 2023-05-29

-   Specified license identifier (was defined as MIT, actual value MIT-0)
-   If SMTP server disconnects with a message, process it and include as part of the response error

## 6.9.2 2023-05-11

-   Fix uncaught exception on invalid attachment content payload

## 6.9.1 2023-01-27

-   Fix base64 encoding for emoji bytes in encoded words

## 6.9.0 2023-01-12

-   Do not throw if failed to resolve IPv4 addresses
-   Include EHLO extensions in the send response
-   fix sendMail function: callback should be optional

## 6.8.0 2022-09-28

-   Add DNS timeout (huksley)
-   add dns.REFUSED (lucagianfelici)

## 6.7.8 2022-08-11

-   Allow to use multiple Reply-To addresses

## 6.7.7 2022-07-06

-   Resolver fixes

## 6.7.5 2022-05-04

-   No changes, pushing a new README to npmjs.org

## 6.7.4 2022-04-29

-   Ensure compatibility with Node 18
-   Replaced Travis with Github Actions

## 6.7.3 2022-03-21

-   Typo fixes
-   Added stale issue automation fir Github
-   Add Infomaniak config to well known service (popod)
-   Update Outlook/Hotmail host in well known services (popod)
-   fix: DSN recipient gets ignored (KornKalle)

## 6.7.2 2021-11-26

-   Fix proxies for account verification

## 6.7.1 2021-11-15

-   fix verify on ses-transport (stanofsky)

## 6.7.0 2021-10-11

-   Updated DNS resolving logic. If there are multiple responses for a A/AAAA record, then loop these randomly instead of only caching the first one

## 6.6.5 2021-09-23

-   Replaced Object.values() and Array.flat() with polyfills to allow using Nodemailer in Node v6+

## 6.6.4 2021-09-22

-   Better compatibility with IPv6-only SMTP hosts (oxzi)
-   Fix ses verify for sdk v3 (hannesvdvreken)
-   Added SECURITY.txt for contact info

## 6.6.3 2021-07-14

-   Do not show passwords in SMTP transaction logs. All passwords used in logging are replaced by `"/* secret */"`

## 6.6.1 2021-05-23

-   Fixed address formatting issue where newlines in an email address, if provided via address object, were not properly removed. Reported by tmazeika (#1289)

## 6.6.0 2021-04-28

-   Added new option `newline` for MailComposer
-   aws ses connection verification (Ognjen Jevremovic)

## 6.5.0 2021-02-26

-   Pass through textEncoding to subnodes
-   Added support for AWS SES v3 SDK
-   Fixed tests

## 6.4.18 2021-02-11

-   Updated README

## 6.4.17 2020-12-11

-   Allow mixing attachments with caendar alternatives

## 6.4.16 2020-11-12

-   Applied updated prettier formating rules

## 6.4.15 2020-11-06

-   Minor changes in header key casing

## 6.4.14 2020-10-14

-   Disabled postinstall script

## 6.4.13 2020-10-02

-   Fix normalizeHeaderKey method for single node messages

## 6.4.12 2020-09-30

-   Better handling of attachment filenames that include quote symbols
-   Includes all information from the oath2 error response in the error message (Normal Gaussian) [1787f227]

## 6.4.11 2020-07-29

-   Fixed escape sequence handling in address parsing

## 6.4.10 2020-06-17

-   Fixed RFC822 output for MailComposer when using invalid content-type value. Mostly relevant if message attachments have stragne content-type values set.

## 6.4.7 2020-05-28

-   Always set charset=utf-8 for Content-Type headers
-   Catch error when using invalid crypto.sign input

## 6.4.6 2020-03-20

-   fix: `requeueAttempts=n` should requeue `n` times (Patrick Malouin) [a27ed2f7]

## 6.4.4 2020-03-01

-   Add `options.forceAuth` for SMTP (Patrick Malouin) [a27ed2f7]

## 6.4.3 2020-02-22

-   Added an option to specify max number of requeues when connection closes unexpectedly (Igor Sechyn) [8a927f5a]

## 6.4.2 2019-12-11

-   Fixed bug where array item was used with a potentially empty array

## 6.4.1 2019-12-07

-   Fix processing server output with unterminated responses

## 6.4.0 2019-12-04

-   Do not use auth if server does not advertise AUTH support [f419b09d]
-   add dns.CONNREFUSED (Hiroyuki Okada) [5c4c8ca8]

## 6.3.1 2019-10-09

-   Ignore "end" events because it might be "error" after it (dex4er) [72bade9]
-   Set username and password on the connection proxy object correctly (UsamaAshraf) [250b1a8]
-   Support more DNS errors (madarche) [2391aa4]

## 6.3.0 2019-07-14

-   Added new option to pass a set of httpHeaders to be sent when fetching attachments. See [PR #1034](https://github.com/nodemailer/nodemailer/pull/1034)

## 6.2.1 2019-05-24

-   No changes. It is the same as 6.2.0 that was accidentally published as 6.2.1 to npm

## 6.2.0 2019-05-24

-   Added new option for addressparser: `flatten`. If true then ignores group names and returns a single list of all addresses

## 6.1.1 2019-04-20

-   Fixed regression bug with missing smtp `authMethod` property

## 6.1.0 2019-04-06

-   Added new message property `amp` for providing AMP4EMAIL content

## 6.0.0 2019-03-25

-   SMTPConnection: use removeListener instead of removeAllListeners (xr0master) [ddc4af15]
    Using removeListener should fix memory leak with Node.js streams

## 5.1.1 2019-01-09

-   Added missing option argument for custom auth

## 5.1.0 2019-01-09

-   Official support for custom authentication methods and examples (examples/custom-auth-async.js and examples/custom-auth-cb.js)

## 5.0.1 2019-01-09

-   Fixed regression error to support Node versions lower than 6.11
-   Added expiremental custom authentication support

## 5.0.0 2018-12-28

-   Start using dns.resolve() instead of dns.lookup() for resolving SMTP hostnames. Might be breaking change on some environments so upgrade with care
-   Show more logs for renewing OAuth2 tokens, previously it was not possible to see what actually failed

## 4.7.0 2018-11-19

-   Cleaned up List-\* header generation
-   Fixed 'full' return option for DSN (klaronix) [23b93a3b]
-   Support promises `for mailcomposer.build()`

## 4.6.8 2018-08-15

-   Use first IP address from DNS resolution when using a proxy (Limbozz) [d4ca847c]
-   Return raw email from SES transport (gabegorelick) [3aa08967]

## 4.6.7 2018-06-15

-   Added option `skipEncoding` to JSONTransport

## 4.6.6 2018-06-10

-   Fixes mime encoded-word compatibility issue with invalid clients like Zimbra

## 4.6.5 2018-05-23

-   Fixed broken DKIM stream in Node.js v10
-   Updated error messages for SMTP responses to not include a newline

## 4.6.4 2018-03-31

-   Readded logo author link to README that was accidentally removed a while ago

## 4.6.3 2018-03-13

-   Removed unneeded dependency

## 4.6.2 2018-03-06

-   When redirecting URL calls then do not include original POST content

## 4.6.1 2018-03-06

-   Fixed Smtp connection freezing, when trying to send after close / quit (twawszczak) [73d3911c]

## 4.6.0 2018-02-22

-   Support socks module v2 in addition to v1 [e228bcb2]
-   Fixed invalid promise return value when using createTestAccount [5524e627]
-   Allow using local addresses [8f6fa35f]

## 4.5.0 2018-02-21

-   Added new message transport option `normalizeHeaderKey(key)=>normalizedKey` for custom header formatting

## 4.4.2 2018-01-20

-   Added sponsors section to README
-   enclose encodeURIComponent in try..catch to handle invalid urls

## 4.4.1 2017-12-08

-   Better handling of unexpectedly dropping connections

## 4.4.0 2017-11-10

-   Changed default behavior for attachment option contentTransferEncoding. If it is unset then base64 encoding is used for the attachment. If it is set to false then previous default applies (base64 for most, 7bit for text)

## 4.3.1 2017-10-25

-   Fixed a confict with Electron.js where timers do not have unref method

## 4.3.0 2017-10-23

-   Added new mail object method `mail.normalize(cb)` that should make creating HTTP API based transports much easier

## 4.2.0 2017-10-13

-   Expose streamed messages size and timers in info response

## v4.1.3 2017-10-06

-   Allow generating preview links without calling createTestAccount first

## v4.1.2 2017-10-03

-   No actual changes. Needed to push updated README to npmjs

## v4.1.1 2017-09-25

-   Fixed JSONTransport attachment handling

## v4.1.0 2017-08-28

-   Added new methods `createTestAccount` and `getTestMessageUrl` to use autogenerated email accounts from https://Ethereal.email

## v4.0.1 2017-04-13

-   Fixed issue with LMTP and STARTTLS

## v4.0.0 2017-04-06

-   License changed from EUPLv1.1 to MIT

## v3.1.8 2017-03-21

-   Fixed invalid List-\* header generation

## v3.1.7 2017-03-14

-   Emit an error if STARTTLS ends with connection being closed

## v3.1.6 2017-03-14

-   Expose last server response for smtpConnection

## v3.1.5 2017-03-08

-   Fixed SES transport, added missing `response` value

## v3.1.4 2017-02-26

-   Fixed DKIM calculation for empty body
-   Ensure linebreak after message content. This fixes DKIM signatures for non-multipart messages where input did not end with a newline

## v3.1.3 2017-02-17

-   Fixed missing `transport.verify()` methods for SES transport

## v3.1.2 2017-02-17

-   Added missing error handlers for Sendmail, SES and Stream transports. If a messages contained an invalid URL as attachment then these transports threw an uncatched error

## v3.1.1 2017-02-13

-   Fixed missing `transport.on('idle')` and `transport.isIdle()` methods for SES transports

## v3.1.0 2017-02-13

-   Added built-in transport for AWS SES. [Docs](http://localhost:1313/transports/ses/)
-   Updated stream transport to allow building JSON strings. [Docs](http://localhost:1313/transports/stream/#json-transport)
-   Added new method _mail.resolveAll_ that fetches all attachments and such to be able to more easily build API-based transports

## v3.0.2 2017-02-04

-   Fixed a bug with OAuth2 login where error callback was fired twice if getToken was not available.

## v3.0.1 2017-02-03

-   Fixed a bug where Nodemailer threw an exception if `disableFileAccess` option was used
-   Added FLOSS [exception declaration](FLOSS_EXCEPTIONS.md)

## v3.0.0 2017-01-31

-   Initial version of Nodemailer 3

This update brings a lot of breaking changes:

-   License changed from MIT to **EUPL-1.1**. This was possible as the new version of Nodemailer is a major rewrite. The features I don't have ownership for, were removed or reimplemented. If there's still some snippets in the code that have vague ownership then notify <mailto:<EMAIL>> about the conflicting code and I'll fix it.
-   Requires **Node.js v6+**
-   All **templating is gone**. It was too confusing to use and to be really universal a huge list of different renderers would be required. Nodemailer is about email, not about parsing different template syntaxes
-   **No NTLM authentication**. It was too difficult to re-implement. If you still need it then it would be possible to introduce a pluggable SASL interface where you could load the NTLM module in your own code and pass it to Nodemailer. Currently this is not possible.
-   **OAuth2 authentication** is built in and has a different [configuration](https://nodemailer.com/smtp/oauth2/). You can use both user (3LO) and service (2LO) accounts to generate access tokens from Nodemailer. Additionally there's a new feature to authenticate differently for every message – useful if your application sends on behalf of different users instead of a single sender.
-   **Improved Calendaring**. Provide an ical file to Nodemailer to send out [calendar events](https://nodemailer.com/message/calendar-events/).

And also some non-breaking changes:

-   All **dependencies were dropped**. There is exactly 0 dependencies needed to use Nodemailer. This brings the installation time of Nodemailer from NPM down to less than 2 seconds
-   **Delivery status notifications** added to Nodemailer
-   Improved and built-in **DKIM** signing of messages. Previously you needed an external module for this and it did quite a lousy job with larger messages
-   **Stream transport** to return a RFC822 formatted message as a stream. Useful if you want to use Nodemailer as a preprocessor and not for actual delivery.
-   **Sendmail** transport built-in, no need for external transport plugin

See [Nodemailer.com](https://nodemailer.com/) for full documentation

## 2.7.0 2016-12-08

-   Bumped mailcomposer that generates encoded-words differently which might break some tests

## 2.6.0 2016-09-05

-   Added new options disableFileAccess and disableUrlAccess
-   Fixed envelope handling where cc/bcc fields were ignored in the envelope object

## 2.4.2 2016-05-25

-   Removed shrinkwrap file. Seemed to cause more trouble than help

## 2.4.1 2016-05-12

-   Fixed outdated shrinkwrap file

## 2.4.0 2016-05-11

-   Bumped mailcomposer module to allow using `false` as attachment filename (suppresses filename usage)
-   Added NTLM authentication support

## 2.3.2 2016-04-11

-   Bumped smtp transport modules to get newest smtp-connection that fixes SMTPUTF8 support for internationalized email addresses

## 2.3.1 2016-04-08

-   Bumped mailcomposer to have better support for message/822 attachments

## 2.3.0 2016-03-03

-   Fixed a bug with attachment filename that contains mixed unicode and dashes
-   Added built-in support for proxies by providing a new SMTP option `proxy` that takes a proxy configuration url as its value
-   Added option `transport` to dynamically load transport plugins
-   Do not require globally installed grunt-cli

## 2.2.1 2016-02-20

-   Fixed a bug in SMTP requireTLS option that was broken

## 2.2.0 2016-02-18

-   Removed the need to use `clone` dependency
-   Added new method `verify` to check SMTP configuration
-   Direct transport uses STARTTLS by default, fallbacks to plaintext if STARTTLS fails
-   Added new message option `list` for setting List-\* headers
-   Add simple proxy support with `getSocket` method
-   Added new message option `textEncoding`. If `textEncoding` is not set then detect best encoding automatically
-   Added new message option `icalEvent` to embed iCalendar events. Example [here](examples/ical-event.js)
-   Added new attachment option `raw` to use prepared MIME contents instead of generating a new one. This might be useful when you want to handcraft some parts of the message yourself, for example if you want to inject a PGP encrypted message as the contents of a MIME node
-   Added new message option `raw` to use an existing MIME message instead of generating a new one

## 2.1.0 2016-02-01

Republishing 2.1.0-rc.1 as stable. To recap, here's the notable changes between v2.0 and v2.1:

-   Implemented templating support. You can either use a simple built-in renderer or some external advanced renderer, eg. [node-email-templates](https://github.com/niftylettuce/node-email-templates). Templating [docs](http://nodemailer.com/2-0-0-beta/templating/).
-   Updated smtp-pool to emit 'idle' events in order to handle message queue more effectively
-   Updated custom header handling, works everywhere the same now, no differences between adding custom headers to the message or to an attachment

## 2.1.0-rc.1 2016-01-25

Sneaked in some new features even though it is already rc

-   If a SMTP pool is closed while there are still messages in a queue, the message callbacks are invoked with an error
-   In case of SMTP pool the transporter emits 'idle' when there is a free connection slot available
-   Added method `isIdle()` that checks if a pool has still some free connection slots available

## 2.1.0-rc.0 2016-01-20

-   Bumped dependency versions

## 2.1.0-beta.3 2016-01-20

-   Added support for node-email-templates templating in addition to the built-in renderer

## 2.1.0-beta.2 2016-01-20

-   Implemented simple templating feature

## 2.1.0-beta.1 2016-01-20

-   Allow using prepared header values that are not folded or encoded by Nodemailer

## 2.1.0-beta.0 2016-01-20

-   Use the same header custom structure for message root, attachments and alternatives
-   Ensure that Message-Id exists when accessing message
-   Allow using array values for custom headers (inserts every value in its own row)

## 2.0.0 2016-01-11

-   Released rc.2 as stable

## 2.0.0-rc.2 2016-01-04

-   Locked dependencies

## 2.0.0-beta.2 2016-01-04

-   Updated documentation to reflect changes with SMTP handling
-   Use beta versions for smtp/pool/direct transports
-   Updated logging

## 2.0.0-beta.1 2016-01-03

-   Use bunyan compatible logger instead of the emit('log') style
-   Outsourced some reusable methods to nodemailer-shared
-   Support setting direct/smtp/pool with the default configuration

## 2.0.0-beta.0 2015-12-31

-   Stream errors are not silently swallowed
-   Do not use format=flowed
-   Use nodemailer-fetch to fetch URL streams
-   jshint replaced by eslint

## v1.11.0 2015-12-28

Allow connection url based SMTP configurations

## v1.10.0 2015-11-13

Added `defaults` argument for `createTransport` to predefine commonn values (eg. `from` address)

## v1.9.0 2015-11-09

Returns a Promise for `sendMail` if callback is not defined

## v1.8.0 2015-10-08

Added priority option (high, normal, low) for setting Importance header

## v1.7.0 2015-10-06

Replaced hyperquest with needle. Fixes issues with compressed data and redirects

## v1.6.0 2015-10-05

Maintenance release. Bumped dependencies to get support for unicode filenames for QQ webmail and to support emoji in filenames

## v1.5.0 2015-09-24

Use mailcomposer instead of built in solution to generate message sources. Bumped libmime gives better quoted-printable handling.

## v1.4.0 2015-06-27

Added new message option `watchHtml` to specify Apple Watch specific HTML part of the message. See [this post](https://litmus.com/blog/how-to-send-hidden-version-email-apple-watch) for details

## v1.3.4 2015-04-25

Maintenance release, bumped buildmail version to get fixed format=flowed handling

## v1.3.3 2015-04-25

Maintenance release, bumped dependencies

## v1.3.2 2015-03-09

Maintenance release, upgraded dependencies. Replaced simplesmtp based tests with smtp-server based ones.

## v1.3.0 2014-09-12

Maintenance release, upgrades buildmail and libmime. Allows using functions as transform plugins and fixes issue with unicode filenames in Gmail.

## v1.2.2 2014-09-05

Proper handling of data uris as attachments. Attachment `path` property can also be defined as a data uri, not just regular url or file path.

## v1.2.1 2014-08-21

Bumped libmime and mailbuild versions to properly handle filenames with spaces (short ascii only filenames with spaces were left unquoted).

## v1.2.0 2014-08-18

Allow using encoded strings as attachments. Added new property `encoding` which defines the encoding used for a `content` string. If encoding is set, the content value is converted to a Buffer value using the defined encoding before usage. Useful for including binary attachemnts in JSON formatted email objects.

## v1.1.2 2014-08-18

Return deprecatin error for v0.x style configuration

## v1.1.1 2014-07-30

Bumped nodemailer-direct-transport dependency. Updated version includes a bugfix for Stream nodes handling. Important only if use direct-transport with Streams (not file paths or urls) as attachment content.

## v1.1.0 2014-07-29

Added new method `resolveContent()` to get the html/text/attachment content as a String or Buffer.

## v1.0.4 2014-07-23

Bugfix release. HTML node was instered twice if the message consisted of a HTML content (but no text content) + at least one attachment with CID + at least one attachment without CID. In this case the HTML node was inserted both to the root level multipart/mixed section and to the multipart/related sub section

## v1.0.3 2014-07-16

Fixed a bug where Nodemailer crashed if the message content type was multipart/related

## v1.0.2 2014-07-16

Upgraded nodemailer-smtp-transport to 0.1.11\. The docs state that for SSL you should use 'secure' option but the underlying smtp-connection module used 'secureConnection' for this purpose. Fixed smpt-connection to match the docs.

## v1.0.1 2014-07-15

Implemented missing #close method that is passed to the underlying transport object. Required by the smtp pool.

## v1.0.0 2014-07-15

Total rewrite. See migration guide here: <http://www.andrisreinman.com/nodemailer-v1-0/#migrationguide>

## v0.7.1 2014-07-09

-   Upgraded aws-sdk to 2.0.5

## v0.7.0 2014-06-17

-   Bumped version to v0.7.0
-   Fix AWS-SES usage [5b6bc144]
-   Replace current SES with new SES using AWS-SDK (Elanorr) [c79d797a]
-   Updated README.md about Node Email Templates (niftylettuce) [e52bef81]

## v0.6.5 2014-05-15

-   Bumped version to v0.6.5
-   Use tildes instead of carets for dependency listing [5296ce41]
-   Allow clients to set a custom identityString (venables) [5373287d]
-   bugfix (adding "-i" to sendmail command line for each new mail) by copying this.args (vrodic) [05a8a9a3]
-   update copyright (gdi2290) [3a6cba3a]

## v0.6.4 2014-05-13

-   Bumped version to v0.6.4
-   added npmignore, bumped dependencies [21bddcd9]
-   Add AOL to well-known services (msouce) [da7dd3b7]

## v0.6.3 2014-04-16

-   Bumped version to v0.6.3
-   Upgraded simplesmtp dependency [dd367f59]

## v0.6.2 2014-04-09

-   Bumped version to v0.6.2
-   Added error option to Stub transport [c423acad]
-   Use SVG npm badge (t3chnoboy) [677117b7]
-   add SendCloud to well known services (haio) [43c358e0]
-   High-res build-passing and NPM module badges (sahat) [9fdc37cd]

## v0.6.1 2014-01-26

-   Bumped version to v0.6.1
-   Do not throw on multiple errors from sendmail command [c6e2cd12]
-   Do not require callback for pickup, fixes #238 [93eb3214]
-   Added AWSSecurityToken information to README, fixes #235 [58e921d1]
-   Added Nodemailer logo [06b7d1a8]

## v0.6.0 2013-12-30

-   Bumped version to v0.6.0
-   Allow defining custom transport methods [ec5b48ce]
-   Return messageId with responseObject for all built in transport methods [74445cec]
-   Bumped dependency versions for mailcomposer and readable-stream [9a034c34]
-   Changed pickup argument name to 'directory' [01c3ea53]
-   Added support for IIS pickup directory with PICKUP transport (philipproplesch) [36940b59..360a2878]
-   Applied common styles [9e93a409]
-   Updated readme [c78075e7]

## v0.5.15 2013-12-13

-   bumped version to v0.5.15
-   Updated README, added global options info for setting uo transports [554bb0e5]
-   Resolve public hostname, if resolveHostname property for a transport object is set to `true` [9023a6e1..4c66b819]

## v0.5.14 2013-12-05

-   bumped version to v0.5.14
-   Expose status for direct messages [f0312df6]
-   Allow to skip the X-Mailer header if xMailer value is set to 'false' [f2c20a68]

## v0.5.13 2013-12-03

-   bumped version to v0.5.13
-   Use the name property from the transport object to use for the domain part of message-id values (1598eee9)

## v0.5.12 2013-12-02

-   bumped version to v0.5.12
-   Expose transport method and transport module version if available [a495106e]
-   Added 'he' module instead of using custom html entity decoding [c197d102]
-   Added xMailer property for transport configuration object to override X-Mailer value [e8733a61]
-   Updated README, added description for 'mail' method [e1f5f3a6]

## v0.5.11 2013-11-28

-   bumped version to v0.5.11
-   Updated mailcomposer version. Replaces ent with he [6a45b790e]

## v0.5.10 2013-11-26

-   bumped version to v0.5.10
-   added shorthand function mail() for direct transport type [88129bd7]
-   minor tweaks and typo fixes [f797409e..ceac0ca4]

## v0.5.9 2013-11-25

-   bumped version to v0.5.9
-   Update for 'direct' handling [77b84e2f]
-   do not require callback to be provided for 'direct' type [ec51c79f]

## v0.5.8 2013-11-22

-   bumped version to v0.5.8
-   Added support for 'direct' transport [826f226d..0dbbcbbc]

## v0.5.7 2013-11-18

-   bumped version to v0.5.7
-   Replace \r\n by \n in Sendmail transport (rolftimmermans) [fed2089e..616ec90c] A lot of sendmail implementations choke on \r\n newlines and require \n This commit addresses this by transforming all \r\n sequences passed to the sendmail command with \n

## v0.5.6 2013-11-15

-   bumped version to v0.5.6
-   Upgraded mailcomposer dependency to 0.2.4 [e5ff9c40]
-   Removed noCR option [e810d1b8]
-   Update wellknown.js, added FastMail (k-j-kleist) [cf930f6d]

## v0.5.5 2013-10-30

-   bumped version to v0.5.5
-   Updated mailcomposer dependnecy version to 0.2.3
-   Remove legacy code - node v0.4 is not supported anymore anyway
-   Use hostname (autodetected or from the options.name property) for Message-Id instead of "Nodemailer" (helps a bit when messages are identified as spam)
-   Added maxMessages info to README

## v0.5.4 2013-10-29

-   bumped version to v0.5.4
-   added "use strict" statements
-   Added DSN info to README
-   add support for QQ enterprise email (coderhaoxin)
-   Add a Bitdeli Badge to README
-   DSN options Passthrought into simplesmtp. (irvinzz)

## v0.5.3 2013-10-03

-   bumped version v0.5.3
-   Using a stub transport to prevent sendmail from being called during a test. (jsdevel)
-   closes #78: sendmail transport does not work correctly on Unix machines. (jsdevel)
-   Updated PaaS Support list to include Modulus. (fiveisprime)
-   Translate self closing break tags to newline (kosmasgiannis)
-   fix typos (aeosynth)

## v0.5.2 2013-07-25

-   bumped version v0.5.2
-   Merge pull request #177 from MrSwitch/master Fixing Amazon SES, fatal error caused by bad connection
