{"version": 3, "file": "Connection.cjs", "names": ["_net", "require", "_tls2", "_interopRequireDefault", "_events", "_stream", "_constant", "obj", "__esModule", "default", "Pop3Connection", "EventEmitter", "constructor", "host", "port", "tls", "timeout", "tlsOptions", "servername", "_socket", "_command", "_updateStream", "Readable", "read", "_pushStream", "buffer", "TERMINATOR_BUFFER_ARRAY", "some", "_buffer", "equals", "_endStream", "end<PERSON><PERSON><PERSON>", "subarray", "TERMINATOR_BUFFER", "push", "err", "emit", "connect", "socket", "Socket", "setKeepAlive", "Promise", "resolve", "reject", "setTimeout", "Error", "eventName", "listeners", "length", "end", "options", "_tls", "on", "command", "firstLineEndIndex", "indexOf", "CRLF_BUFFER", "infoBuffer", "commandName", "msgNumber", "split", "stream", "MULTI_LINE_COMMAND_NAME", "includes", "MAYBE_MULTI_LINE_COMMAND_NAME", "bodyBuffer", "toString", "once", "args", "join", "rejectFn", "info", "removeListener", "write", "CRLF", "_default", "exports", "module"], "sources": ["../src/Connection.js"], "sourcesContent": ["import {Socket} from 'net';\nimport _tls from 'tls';\nimport {EventEmitter} from 'events';\nimport {Readable} from 'stream';\n\nimport {\n  CRLF,\n  CRLF_BUFFER,\n  TERMINATOR_BUFFER,\n  TERMINATOR_BUFFER_ARRAY,\n  MULTI_LINE_COMMAND_NAME,\n  MAYBE_MULTI_LINE_COMMAND_NAME\n} from './constant.js';\n\n/**\n * @typedef {number} Integer\n */\n\n/* eslint-disable unicorn/prefer-event-target -- Should replace for browser */\n/**\n *\n */\nclass Pop3Connection extends EventEmitter {\n  /* eslint-enable unicorn/prefer-event-target -- Should replace for browser */\n  /**\n   * @param {{\n  *   host: string,\n  *   port?: Integer,\n  *   tls?: boolean,\n  *   timeout?: Integer,\n  *   tlsOptions?: import('tls').TlsOptions,\n  *   servername?: string\n  * }} cfg\n  */\n  constructor ({\n    host,\n    port,\n    tls,\n    timeout,\n    tlsOptions,\n    servername\n  }) {\n    super();\n    this.host = host;\n    this.port = port || (tls ? 995 : 110);\n    this.tls = tls;\n    this.timeout = timeout;\n    this._socket = null;\n    this._stream = null;\n    this._command = '';\n    this.tlsOptions = tlsOptions || {};\n    this.servername = servername || host;\n  }\n\n  /**\n   * @returns {Readable}\n   */\n  _updateStream () {\n    this._stream = new Readable({\n      read () {\n        //\n      }\n    });\n    return this._stream;\n  }\n\n  /**\n   * @param {Buffer} buffer\n   * @returns {void}\n   */\n  _pushStream (buffer) {\n    if (TERMINATOR_BUFFER_ARRAY.some((_buffer) => _buffer.equals(buffer))) {\n      this._endStream();\n      return;\n    }\n    const endBuffer = buffer.subarray(-5);\n    if (endBuffer.equals(TERMINATOR_BUFFER)) {\n      /** @type {Readable} */ (this._stream).push(buffer.subarray(0, -5));\n      this._endStream();\n      return;\n    }\n    /** @type {Readable} */ (this._stream).push(buffer);\n  }\n\n  /**\n   * @param {Error} [err]\n   * @returns {void}\n   */\n  _endStream (err) {\n    if (this._stream) {\n      this._stream.push(null);\n    }\n    this._stream = null;\n    this.emit('end', err);\n  }\n\n  /**\n   * @returns {Promise<void>}\n   */\n  connect () {\n    const {host, port, tlsOptions, servername} = this;\n    const socket = new Socket();\n    socket.setKeepAlive(true);\n    // eslint-disable-next-line promise/avoid-new -- Our own API\n    return new Promise((\n      /** @type {(val?: any) => void} */\n      resolve,\n      reject\n    ) => {\n      if (typeof this.timeout !== 'undefined') {\n        socket.setTimeout(this.timeout, () => {\n          const err = /** @type {Error & {eventName: \"timeout\"}} */ (\n            new Error('timeout')\n          );\n          err.eventName = 'timeout';\n          reject(err);\n          if (this.listeners('end').length) {\n            this.emit('end', err);\n          }\n          if (this.listeners('error').length) {\n            this.emit('error', err);\n          }\n          /** @type {import('tls').TLSSocket} */ (this._socket).end();\n          this._socket = null;\n        });\n      }\n      if (this.tls) {\n        const options = {host, port, socket, servername, ...tlsOptions};\n        // @ts-expect-error Works\n        this._socket = _tls.connect(options);\n      } else {\n        this._socket = socket;\n      }\n\n      this._socket.on(\n        'data',\n        /**\n         * @param {Buffer} buffer\n         * @returns {void}\n         */\n        (buffer) => {\n          if (this._stream) {\n            this._pushStream(buffer);\n            return;\n          }\n          if (buffer[0] === 45) { // '-'\n            const err =\n              /**\n               * @type {Error & {eventName: \"error\", command: string|undefined}}\n               */ (\n                new Error(buffer.subarray(5, -2))\n              );\n            err.eventName = 'error';\n            err.command = this._command;\n            this.emit('error', err);\n            return;\n          }\n          if (buffer[0] === 43) { // '+'\n            const firstLineEndIndex = buffer.indexOf(CRLF_BUFFER);\n            const infoBuffer = buffer.subarray(4, firstLineEndIndex);\n            const [commandName, msgNumber] = (this._command || '').split(' ');\n            let stream = null;\n            if (MULTI_LINE_COMMAND_NAME.includes(commandName) ||\n                (!msgNumber &&\n                MAYBE_MULTI_LINE_COMMAND_NAME.includes(commandName))) {\n              this._updateStream();\n              stream = this._stream;\n              const bodyBuffer = buffer.subarray(firstLineEndIndex + 2);\n              if (bodyBuffer[0]) {\n                this._pushStream(bodyBuffer);\n              }\n            }\n            this.emit('response', infoBuffer.toString(), stream);\n            resolve();\n            return;\n          }\n          const err =\n            /**\n             * @type {Error & {eventName: \"bad-server-response\"}}\n             */ (\n              new Error('Unexpected response')\n            );\n          err.eventName = 'bad-server-response';\n          reject(err);\n        }\n      );\n      this._socket.on('error', (err) => {\n        err.eventName = 'error';\n        if (this._stream) {\n          this.emit('error', err);\n          return;\n        }\n        reject(err);\n        this._socket = null;\n      });\n      this._socket.once('close', () => {\n        const err = /** @type {Error & {eventName: \"close\"}} */ (\n          new Error('close')\n        );\n        err.eventName = 'close';\n        reject(err);\n        this._socket = null;\n      });\n      this._socket.once('end', () => {\n        const err = /** @type {Error & {eventName: \"end\"}} */ (\n          new Error('end')\n        );\n        err.eventName = 'end';\n        reject(err);\n        this._socket = null;\n      });\n      socket.connect({\n        host,\n        port\n      });\n    });\n  }\n\n  /**\n   * @param {...(string|Integer)} args\n   * @throws {Error}\n   * @returns {Promise<[string, Readable]>}\n   */\n  async command (...args) {\n    this._command = args.join(' ');\n    if (!this._socket) {\n      throw new Error('no-socket');\n    }\n    // eslint-disable-next-line promise/avoid-new -- Our own API\n    await new Promise((\n      /** @type {(value?: any) => void} */\n      resolve,\n      reject\n    ) => {\n      if (!this._stream) {\n        resolve();\n        return;\n      }\n      this.once('error', (err) => {\n        reject(err);\n      });\n      this.once('end', (err) => {\n        return err ? reject(err) : resolve();\n      });\n    });\n\n    // eslint-disable-next-line promise/avoid-new -- Our own API\n    return new Promise((resolve, reject) => {\n      /**\n       * @param {Error} err\n       */\n      const rejectFn = (err) => reject(err);\n      this.once('error', rejectFn);\n      this.once('response', (info, stream) => {\n        this.removeListener('error', rejectFn);\n        resolve([info, stream]);\n      });\n      if (!this._socket) {\n        reject(new Error('no-socket'));\n      }\n      /** @type {Socket} */ (\n        this._socket\n      ).write(`${this._command}${CRLF}`, 'utf8');\n    });\n  }\n}\n\nexport default Pop3Connection;\n"], "mappings": ";;;;;;AAAA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAEA,IAAAK,SAAA,GAAAL,OAAA;AAOuB,SAAAE,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEvB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAMG,cAAc,SAASC,oBAAY,CAAC;EACxC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAAE;IACXC,IAAI;IACJC,IAAI;IACJC,GAAG;IACHC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,EAAE;IACD,KAAK,CAAC,CAAC;IACP,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI,KAAKC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IACrC,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACd,OAAO,GAAG,IAAI;IACnB,IAAI,CAACe,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACH,UAAU,GAAGA,UAAU,IAAI,CAAC,CAAC;IAClC,IAAI,CAACC,UAAU,GAAGA,UAAU,IAAIL,IAAI;EACtC;;EAEA;AACF;AACA;EACEQ,aAAaA,CAAA,EAAI;IACf,IAAI,CAAChB,OAAO,GAAG,IAAIiB,gBAAQ,CAAC;MAC1BC,IAAIA,CAAA,EAAI;QACN;MAAA;IAEJ,CAAC,CAAC;IACF,OAAO,IAAI,CAAClB,OAAO;EACrB;;EAEA;AACF;AACA;AACA;EACEmB,WAAWA,CAAEC,MAAM,EAAE;IACnB,IAAIC,iCAAuB,CAACC,IAAI,CAAEC,OAAO,IAAKA,OAAO,CAACC,MAAM,CAACJ,MAAM,CAAC,CAAC,EAAE;MACrE,IAAI,CAACK,UAAU,CAAC,CAAC;MACjB;IACF;IACA,MAAMC,SAAS,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrC,IAAID,SAAS,CAACF,MAAM,CAACI,2BAAiB,CAAC,EAAE;MACvC,uBAAyB,IAAI,CAAC5B,OAAO,CAAE6B,IAAI,CAACT,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnE,IAAI,CAACF,UAAU,CAAC,CAAC;MACjB;IACF;IACA;IAAyB,IAAI,CAACzB,OAAO,CAAE6B,IAAI,CAACT,MAAM,CAAC;EACrD;;EAEA;AACF;AACA;AACA;EACEK,UAAUA,CAAEK,GAAG,EAAE;IACf,IAAI,IAAI,CAAC9B,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAAC6B,IAAI,CAAC,IAAI,CAAC;IACzB;IACA,IAAI,CAAC7B,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC+B,IAAI,CAAC,KAAK,EAAED,GAAG,CAAC;EACvB;;EAEA;AACF;AACA;EACEE,OAAOA,CAAA,EAAI;IACT,MAAM;MAACxB,IAAI;MAAEC,IAAI;MAAEG,UAAU;MAAEC;IAAU,CAAC,GAAG,IAAI;IACjD,MAAMoB,MAAM,GAAG,IAAIC,WAAM,CAAC,CAAC;IAC3BD,MAAM,CAACE,YAAY,CAAC,IAAI,CAAC;IACzB;IACA,OAAO,IAAIC,OAAO,CAAC,EACjB;IACAC,OAAO,EACPC,MAAM,KACH;MACH,IAAI,OAAO,IAAI,CAAC3B,OAAO,KAAK,WAAW,EAAE;QACvCsB,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC5B,OAAO,EAAE,MAAM;UACpC,MAAMmB,GAAG,GAAG;UACV,IAAIU,KAAK,CAAC,SAAS,CACpB;UACDV,GAAG,CAACW,SAAS,GAAG,SAAS;UACzBH,MAAM,CAACR,GAAG,CAAC;UACX,IAAI,IAAI,CAACY,SAAS,CAAC,KAAK,CAAC,CAACC,MAAM,EAAE;YAChC,IAAI,CAACZ,IAAI,CAAC,KAAK,EAAED,GAAG,CAAC;UACvB;UACA,IAAI,IAAI,CAACY,SAAS,CAAC,OAAO,CAAC,CAACC,MAAM,EAAE;YAClC,IAAI,CAACZ,IAAI,CAAC,OAAO,EAAED,GAAG,CAAC;UACzB;UACA;UAAwC,IAAI,CAAChB,OAAO,CAAE8B,GAAG,CAAC,CAAC;UAC3D,IAAI,CAAC9B,OAAO,GAAG,IAAI;QACrB,CAAC,CAAC;MACJ;MACA,IAAI,IAAI,CAACJ,GAAG,EAAE;QACZ,MAAMmC,OAAO,GAAG;UAACrC,IAAI;UAAEC,IAAI;UAAEwB,MAAM;UAAEpB,UAAU;UAAE,GAAGD;QAAU,CAAC;QAC/D;QACA,IAAI,CAACE,OAAO,GAAGgC,aAAI,CAACd,OAAO,CAACa,OAAO,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAAC/B,OAAO,GAAGmB,MAAM;MACvB;MAEA,IAAI,CAACnB,OAAO,CAACiC,EAAE,CACb,MAAM;MACN;AACR;AACA;AACA;MACS3B,MAAM,IAAK;QACV,IAAI,IAAI,CAACpB,OAAO,EAAE;UAChB,IAAI,CAACmB,WAAW,CAACC,MAAM,CAAC;UACxB;QACF;QACA,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;UAAE;UACtB,MAAMU,GAAG;UACP;AACd;AACA;UACgB,IAAIU,KAAK,CAACpB,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACjC;UACHG,GAAG,CAACW,SAAS,GAAG,OAAO;UACvBX,GAAG,CAACkB,OAAO,GAAG,IAAI,CAACjC,QAAQ;UAC3B,IAAI,CAACgB,IAAI,CAAC,OAAO,EAAED,GAAG,CAAC;UACvB;QACF;QACA,IAAIV,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;UAAE;UACtB,MAAM6B,iBAAiB,GAAG7B,MAAM,CAAC8B,OAAO,CAACC,qBAAW,CAAC;UACrD,MAAMC,UAAU,GAAGhC,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAEsB,iBAAiB,CAAC;UACxD,MAAM,CAACI,WAAW,EAAEC,SAAS,CAAC,GAAG,CAAC,IAAI,CAACvC,QAAQ,IAAI,EAAE,EAAEwC,KAAK,CAAC,GAAG,CAAC;UACjE,IAAIC,MAAM,GAAG,IAAI;UACjB,IAAIC,iCAAuB,CAACC,QAAQ,CAACL,WAAW,CAAC,IAC5C,CAACC,SAAS,IACXK,uCAA6B,CAACD,QAAQ,CAACL,WAAW,CAAE,EAAE;YACxD,IAAI,CAACrC,aAAa,CAAC,CAAC;YACpBwC,MAAM,GAAG,IAAI,CAACxD,OAAO;YACrB,MAAM4D,UAAU,GAAGxC,MAAM,CAACO,QAAQ,CAACsB,iBAAiB,GAAG,CAAC,CAAC;YACzD,IAAIW,UAAU,CAAC,CAAC,CAAC,EAAE;cACjB,IAAI,CAACzC,WAAW,CAACyC,UAAU,CAAC;YAC9B;UACF;UACA,IAAI,CAAC7B,IAAI,CAAC,UAAU,EAAEqB,UAAU,CAACS,QAAQ,CAAC,CAAC,EAAEL,MAAM,CAAC;UACpDnB,OAAO,CAAC,CAAC;UACT;QACF;QACA,MAAMP,GAAG;QACP;AACZ;AACA;QACc,IAAIU,KAAK,CAAC,qBAAqB,CAChC;QACHV,GAAG,CAACW,SAAS,GAAG,qBAAqB;QACrCH,MAAM,CAACR,GAAG,CAAC;MACb,CACF,CAAC;MACD,IAAI,CAAChB,OAAO,CAACiC,EAAE,CAAC,OAAO,EAAGjB,GAAG,IAAK;QAChCA,GAAG,CAACW,SAAS,GAAG,OAAO;QACvB,IAAI,IAAI,CAACzC,OAAO,EAAE;UAChB,IAAI,CAAC+B,IAAI,CAAC,OAAO,EAAED,GAAG,CAAC;UACvB;QACF;QACAQ,MAAM,CAACR,GAAG,CAAC;QACX,IAAI,CAAChB,OAAO,GAAG,IAAI;MACrB,CAAC,CAAC;MACF,IAAI,CAACA,OAAO,CAACgD,IAAI,CAAC,OAAO,EAAE,MAAM;QAC/B,MAAMhC,GAAG,GAAG;QACV,IAAIU,KAAK,CAAC,OAAO,CAClB;QACDV,GAAG,CAACW,SAAS,GAAG,OAAO;QACvBH,MAAM,CAACR,GAAG,CAAC;QACX,IAAI,CAAChB,OAAO,GAAG,IAAI;MACrB,CAAC,CAAC;MACF,IAAI,CAACA,OAAO,CAACgD,IAAI,CAAC,KAAK,EAAE,MAAM;QAC7B,MAAMhC,GAAG,GAAG;QACV,IAAIU,KAAK,CAAC,KAAK,CAChB;QACDV,GAAG,CAACW,SAAS,GAAG,KAAK;QACrBH,MAAM,CAACR,GAAG,CAAC;QACX,IAAI,CAAChB,OAAO,GAAG,IAAI;MACrB,CAAC,CAAC;MACFmB,MAAM,CAACD,OAAO,CAAC;QACbxB,IAAI;QACJC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMuC,OAAOA,CAAE,GAAGe,IAAI,EAAE;IACtB,IAAI,CAAChD,QAAQ,GAAGgD,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC;IAC9B,IAAI,CAAC,IAAI,CAAClD,OAAO,EAAE;MACjB,MAAM,IAAI0B,KAAK,CAAC,WAAW,CAAC;IAC9B;IACA;IACA,MAAM,IAAIJ,OAAO,CAAC,EAChB;IACAC,OAAO,EACPC,MAAM,KACH;MACH,IAAI,CAAC,IAAI,CAACtC,OAAO,EAAE;QACjBqC,OAAO,CAAC,CAAC;QACT;MACF;MACA,IAAI,CAACyB,IAAI,CAAC,OAAO,EAAGhC,GAAG,IAAK;QAC1BQ,MAAM,CAACR,GAAG,CAAC;MACb,CAAC,CAAC;MACF,IAAI,CAACgC,IAAI,CAAC,KAAK,EAAGhC,GAAG,IAAK;QACxB,OAAOA,GAAG,GAAGQ,MAAM,CAACR,GAAG,CAAC,GAAGO,OAAO,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,OAAO,IAAID,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC;AACN;AACA;MACM,MAAM2B,QAAQ,GAAInC,GAAG,IAAKQ,MAAM,CAACR,GAAG,CAAC;MACrC,IAAI,CAACgC,IAAI,CAAC,OAAO,EAAEG,QAAQ,CAAC;MAC5B,IAAI,CAACH,IAAI,CAAC,UAAU,EAAE,CAACI,IAAI,EAAEV,MAAM,KAAK;QACtC,IAAI,CAACW,cAAc,CAAC,OAAO,EAAEF,QAAQ,CAAC;QACtC5B,OAAO,CAAC,CAAC6B,IAAI,EAAEV,MAAM,CAAC,CAAC;MACzB,CAAC,CAAC;MACF,IAAI,CAAC,IAAI,CAAC1C,OAAO,EAAE;QACjBwB,MAAM,CAAC,IAAIE,KAAK,CAAC,WAAW,CAAC,CAAC;MAChC;MACA;MACE,IAAI,CAAC1B,OAAO,CACZsD,KAAK,CAAE,GAAE,IAAI,CAACrD,QAAS,GAAEsD,cAAK,EAAC,EAAE,MAAM,CAAC;IAC5C,CAAC,CAAC;EACJ;AACF;AAAC,IAAAC,QAAA,GAEcjE,cAAc;AAAAkE,OAAA,CAAAnE,OAAA,GAAAkE,QAAA;AAAAE,MAAA,CAAAD,OAAA,GAAAA,OAAA,CAAAnE,OAAA"}