{"version": 3, "file": "fingerprint-injector.js", "sourceRoot": "", "sources": ["../src/fingerprint-injector.ts"], "names": [], "mappings": ";;;AAAA,2BAAkC;AAElC,iEAAsI;AAqBtI;;;GAGG;AACH,MAAa,mBAAmB;IAAhC;QACY;;;;mBAAU,IAAI,CAAC,UAAU,EAAE;WAAC;IAoPxC,CAAC;IAlPG;;;;;OAKG;IACK,qBAAqB,CAAC,OAA+B,EAAE,WAAoB;QAC/E,MAAM,cAAc,GAAG;YACnB,iBAAiB;YACjB,QAAQ;YACR,eAAe;YACf,QAAQ;YACR,gBAAgB;YAChB,gBAAgB;YAChB,gBAAgB;YAChB,gBAAgB;YAChB,2BAA2B;SAC9B,CAAC;QAEF,MAAM,eAAe,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAEvC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC9B,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,iEAAiE;QACjE,yEAAyE;QACzE,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;YAC5D,OAAO,eAAe,CAAC,EAAE,CAAC;SAC7B;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,6BAA6B,CAAC,cAA8B,EAAE,6BAA4D;QAC5H,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,6BAA6B,CAAC;QAC/D,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAElE,MAAM,OAAO,GAAG,IAAI,CAAC,gCAAgC,CACjD,mBAAmB,CACtB,CAAC;QAEF,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QACnE,MAAM,cAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QAE3F,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;iBACrC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,MAAM,cAAc,CAAC,aAAa,CAAC;YAC/B,OAAO;SACV,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,4BAA4B,CAAC,IAAU,EAAE,6BAA4D;QACvG,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,6BAA6B,CAAC;QAC/D,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAClE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,mBAAmB,CAAC;QAElD,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAEnC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,CAAC;QAEtD,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YACnD,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBACjF,YAAY,EAAE,MAAM,CAAC,MAAM;gBAC3B,WAAW,EAAE,MAAM,CAAC,KAAK;gBACzB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,MAAM,EAAE,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC;gBAC/C,iBAAiB,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE;gBACjI,iBAAiB,EAAE,MAAM,CAAC,gBAAgB;aAC7C,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;YAEpF,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAC5B,EAAE,IAAI,EAAE,sBAAsB,EAAE,KAAK,EAAE,MAAM,EAAE;aAClD,CAAC,CAAC;SACN;QAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gCAAgC,CAAC,mBAAmB,CAAC,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,6BAA4D;QAC5E,MAAM,EAAE,WAAW,EAAE,GAAG,6BAA6B,CAAC;QACtD,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC,gCAAgC,CAAC,mBAAmB,CAAC,CAAC;IACtE,CAAC;IAED;;;;OAIG;IACK,gCAAgC,CACpC,WAAgC;QAEhC,SAAS,MAAM;YACX,MAAM,EACF,OAAO,EACP,SAAS,EAAE,EAEP,eAAe,EACf,aAAa,EACb,SAAS,EACT,GAAG,cAAc,EACpB,EACD,MAAM,EAAE,cAAc,EACtB,SAAS,EACT,aAAa,EACb,WAAW,EACX,WAAW,EACX,UAAU,EACV,IAAI;YACJ,yCAAyC;cAC5C,GAAG,EAAyB,CAAC;YAE9B,MAAM;YACF,sBAAsB;YACtB,WAAW,EACX,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,WAAW,EACX,OAAO,EACP,WAAW,EACX,WAAW;YAEX,wBAAwB;YACxB,WAAW,EACX,YAAY;YACZ,sBAAsB;YAEtB,MAAM;YACN,sBAAsB;YACtB,GAAG,SAAS,EACf,GAAG,cAAc,CAAC;YAEnB,MAAM,iBAAiB,GAAG;gBACtB,WAAW;gBACX,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,WAAW;gBACX,gBAAgB;aACnB,CAAC;YACF,MAAM,mBAAmB,GAAG;gBACxB,YAAY;gBACZ,WAAW;aACd,CAAC;YAEF,gBAAgB,EAAE,CAAC;YAEnB,IAAI,UAAU;gBAAE,WAAW,EAAE,CAAC;YAE9B,IAAI,IAAI,EAAE;gBACN,yCAAyC;gBACzC,wCAAwC;gBACxC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;aACzB;YAED,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACzC,cAAc,EAAE,CAAC;YAEjB,IAAI,aAAa,EAAE;gBACf,qBAAqB,CAAC,aAAa,CAAC,CAAC;aACxC;YAED,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE;gBAC3B,cAAsB,CAAC,SAAS,GAAG,KAAK,CAAC;aAC7C;YACD,yBAAyB,CAAC,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAE5D,yBAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACpD,6BAA6B,CAAC,iBAAiB,CAAC,CAAC;YACjD,+BAA+B,CAAC,mBAAmB,CAAC,CAAC;YAErD,yBAAyB,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;YAErE,aAAa,CAAC,SAAS,CAAC,CAAC;YACzB,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAEzC,eAAe,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAED,MAAM,kBAAkB,GAAW,MAAM,CAAC,QAAQ,EAAE,CAAC;QAErD,OAAO,SAAS,IAAI,CAAC,OAAO,cAAc,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,kBAAkB,SAAS,CAAC;IAC3G,CAAC;IAEO,mBAAmB,CAAC,WAAwB;QAChD,MAAM,EACF,SAAS,EACT,GAAG,IAAI,EACV,GAAG,WAAW,CAAC;QAEhB,OAAO;YACH,GAAG,IAAI;YACP,SAAS;YACT,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC;SAC3C,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACK,UAAU;QACd,oIAAoI;QACpI,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,GAAG,SAAS,WAAW,CAAC,CAAC;QACtD,OAAO,KAAK,OAAO,IAAI,CAAC;IAC5B,CAAC;IAEO,cAAc,CAAC,GAAW,EAAE,GAAW;QAC3C,OAAO,IAAI,CAAC,KAAK,CACb,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CACpC,CAAC;IACN,CAAC;CACJ;AArPD,kDAqPC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,kBAAkB,CACpC,OAAkB,EAClB,OAIC;IAED,MAAM,SAAS,GAAG,IAAI,4CAAoB,EAAE,CAAC;IAC7C,MAAM,sBAAsB,GAAG,OAAO,EAAE,WAAW,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,kBAAkB,IAAI,EAAE,CAAC,CAAC;IAEnH,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,sBAAsB,CAAC;IACxD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;QACrC,SAAS,EAAE,WAAW,CAAC,SAAS,CAAC,SAAS;QAC1C,WAAW,EAAE,MAAM;QACnB,GAAG,OAAO,EAAE,iBAAiB;QAC7B,QAAQ,EAAE;YACN,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK;YAC/B,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM;YACjC,GAAG,OAAO,EAAE,iBAAiB,EAAE,QAAQ;SAC1C;QACD,gBAAgB,EAAE;YACd,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,CAAC;YAC7C,GAAG,OAAO,EAAE,iBAAiB,EAAE,gBAAgB;SAClD;KACJ,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAC3C,MAAM,QAAQ,CAAC,6BAA6B,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC;IAE9E,OAAO,OAAO,CAAC;AACnB,CAAC;AA/BD,gDA+BC;AAEM,KAAK,UAAU,eAAe,CACjC,OAAkB,EAClB,OAGC;IAED,MAAM,SAAS,GAAG,IAAI,4CAAoB,EAAE,CAAC;IAC7C,MAAM,sBAAsB,GAAG,OAAO,EAAE,WAAW,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,kBAAkB,IAAI,EAAE,CAAC,CAAC;IAEnH,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;IAErC,MAAM,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAC3C,MAAM,QAAQ,CAAC,4BAA4B,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;IAE1E,OAAO,IAAI,CAAC;AAChB,CAAC;AAhBD,0CAgBC"}