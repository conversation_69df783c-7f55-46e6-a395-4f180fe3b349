{"version": 3, "file": "Command.cjs", "names": ["_Connection", "_interopRequireDefault", "require", "_helper", "obj", "__esModule", "default", "Pop3Command", "Pop3Connection", "constructor", "user", "password", "host", "port", "tls", "timeout", "tlsOptions", "servername", "_PASSInfo", "_connect", "_socket", "connect", "command", "info", "UIDL", "msgNumber", "stream", "listify", "str", "stream2String", "NOOP", "LIST", "RSET", "RETR", "DELE", "STAT", "LAST", "TOP", "numLines", "QUIT", "_default", "exports", "module"], "sources": ["../src/Command.js"], "sourcesContent": ["import Pop3Connection from './Connection.js';\n\nimport {stream2String, listify} from './helper.js';\n\n/**\n * @typedef {number} Integer\n */\n\n/**\n *\n */\nclass Pop3Command extends Pop3Connection {\n  /**\n   * @param {{\n   *   user: string,\n   *   password: string,\n   *   host: string,\n   *   port?: Integer,\n   *   tls?: boolean,\n   *   timeout?: Integer,\n   *   tlsOptions?: import('tls').TlsOptions,\n   *   servername?: string\n   * }} cfg\n   */\n  constructor ({\n    user,\n    password,\n    host,\n    port,\n    tls,\n    timeout,\n    tlsOptions,\n    servername\n  }) {\n    super({host, port, tls, timeout, tlsOptions, servername});\n    this.user = user;\n    this.password = password;\n    this._PASSInfo = '';\n  }\n\n  /**\n   * @returns {Promise<string>}\n   */\n  async _connect () {\n    if (this._socket) {\n      return this._PASSInfo;\n    }\n    await super.connect();\n    await super.command('USER', this.user);\n    const [info] = await super.command('PASS', this.password);\n    this._PASSInfo = info;\n    return this._PASSInfo;\n  }\n\n  /**\n   * @param {Integer|string} msgNumber\n   * @returns {Promise<string[][]|string[]>}\n   */\n  async UIDL (msgNumber = '') {\n    await this._connect();\n    const [info, stream] = await super.command('UIDL', msgNumber);\n    if (msgNumber) {\n      return listify(info)[0];\n    }\n    const str = await stream2String(stream);\n    return listify(str);\n  }\n\n  /**\n   * @returns {Promise<void>}\n   */\n  async NOOP () {\n    await this._connect();\n    await super.command('NOOP');\n  }\n\n  /**\n   * @param {Integer|string} msgNumber\n   * @returns {Promise<string[][]|string[]>}\n   */\n  async LIST (msgNumber = '') {\n    await this._connect();\n    const [info, stream] = await super.command('LIST', msgNumber);\n    if (msgNumber) {\n      return listify(info)[0];\n    }\n    const str = await stream2String(stream);\n    return listify(str);\n  }\n\n  /**\n   * @returns {Promise<string>}\n   */\n  async RSET () {\n    await this._connect();\n    const [info] = await super.command('RSET');\n    return info;\n  }\n\n  /**\n   * @param {Integer} msgNumber\n   * @returns {Promise<string>}\n   */\n  async RETR (msgNumber) {\n    await this._connect();\n    const [, stream] = await super.command('RETR', msgNumber);\n    return stream2String(stream);\n  }\n\n  /**\n   * @param {Integer} msgNumber\n   * @returns {Promise<string>}\n   */\n  async DELE (msgNumber) {\n    await this._connect();\n    const [info] = await super.command('DELE', msgNumber);\n    return info;\n  }\n\n  /**\n   * @returns {Promise<string>}\n   */\n  async STAT () {\n    await this._connect();\n    const [info] = await super.command('STAT');\n    return info;\n  }\n\n  /**\n   * @returns {Promise<string>}\n   */\n  async LAST () {\n    await this._connect();\n    const [info] = await super.command('LAST');\n    // May fail depending on test server\n    /* c8 ignore next */\n    return info;\n  }\n\n  /**\n   * @param {Integer} msgNumber\n   * @param {Integer} numLines\n   * @returns {Promise<string>}\n   */\n  async TOP (msgNumber, numLines = 0) {\n    await this._connect();\n    const [, stream] = await super.command('TOP', msgNumber, numLines);\n    return stream2String(stream);\n  }\n\n  /**\n   * @returns {Promise<string>}\n   */\n  async QUIT () {\n    if (!this._socket) {\n      this._PASSInfo = 'Bye';\n      return this._PASSInfo;\n    }\n    const [info] = await super.command('QUIT');\n    this._PASSInfo = info || '';\n    return this._PASSInfo;\n  }\n}\n\nPop3Command.stream2String = stream2String;\nPop3Command.listify = listify;\n\nexport default Pop3Command;\n"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAAmD,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEnD;AACA;AACA;AAEA;AACA;AACA;AACA,MAAMG,WAAW,SAASC,mBAAc,CAAC;EACvC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAAE;IACXC,IAAI;IACJC,QAAQ;IACRC,IAAI;IACJC,IAAI;IACJC,GAAG;IACHC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,EAAE;IACD,KAAK,CAAC;MAACL,IAAI;MAAEC,IAAI;MAAEC,GAAG;MAAEC,OAAO;MAAEC,UAAU;MAAEC;IAAU,CAAC,CAAC;IACzD,IAAI,CAACP,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACO,SAAS,GAAG,EAAE;EACrB;;EAEA;AACF;AACA;EACE,MAAMC,QAAQA,CAAA,EAAI;IAChB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,OAAO,IAAI,CAACF,SAAS;IACvB;IACA,MAAM,KAAK,CAACG,OAAO,CAAC,CAAC;IACrB,MAAM,KAAK,CAACC,OAAO,CAAC,MAAM,EAAE,IAAI,CAACZ,IAAI,CAAC;IACtC,MAAM,CAACa,IAAI,CAAC,GAAG,MAAM,KAAK,CAACD,OAAO,CAAC,MAAM,EAAE,IAAI,CAACX,QAAQ,CAAC;IACzD,IAAI,CAACO,SAAS,GAAGK,IAAI;IACrB,OAAO,IAAI,CAACL,SAAS;EACvB;;EAEA;AACF;AACA;AACA;EACE,MAAMM,IAAIA,CAAEC,SAAS,GAAG,EAAE,EAAE;IAC1B,MAAM,IAAI,CAACN,QAAQ,CAAC,CAAC;IACrB,MAAM,CAACI,IAAI,EAAEG,MAAM,CAAC,GAAG,MAAM,KAAK,CAACJ,OAAO,CAAC,MAAM,EAAEG,SAAS,CAAC;IAC7D,IAAIA,SAAS,EAAE;MACb,OAAO,IAAAE,eAAO,EAACJ,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,MAAMK,GAAG,GAAG,MAAM,IAAAC,qBAAa,EAACH,MAAM,CAAC;IACvC,OAAO,IAAAC,eAAO,EAACC,GAAG,CAAC;EACrB;;EAEA;AACF;AACA;EACE,MAAME,IAAIA,CAAA,EAAI;IACZ,MAAM,IAAI,CAACX,QAAQ,CAAC,CAAC;IACrB,MAAM,KAAK,CAACG,OAAO,CAAC,MAAM,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;EACE,MAAMS,IAAIA,CAAEN,SAAS,GAAG,EAAE,EAAE;IAC1B,MAAM,IAAI,CAACN,QAAQ,CAAC,CAAC;IACrB,MAAM,CAACI,IAAI,EAAEG,MAAM,CAAC,GAAG,MAAM,KAAK,CAACJ,OAAO,CAAC,MAAM,EAAEG,SAAS,CAAC;IAC7D,IAAIA,SAAS,EAAE;MACb,OAAO,IAAAE,eAAO,EAACJ,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,MAAMK,GAAG,GAAG,MAAM,IAAAC,qBAAa,EAACH,MAAM,CAAC;IACvC,OAAO,IAAAC,eAAO,EAACC,GAAG,CAAC;EACrB;;EAEA;AACF;AACA;EACE,MAAMI,IAAIA,CAAA,EAAI;IACZ,MAAM,IAAI,CAACb,QAAQ,CAAC,CAAC;IACrB,MAAM,CAACI,IAAI,CAAC,GAAG,MAAM,KAAK,CAACD,OAAO,CAAC,MAAM,CAAC;IAC1C,OAAOC,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACE,MAAMU,IAAIA,CAAER,SAAS,EAAE;IACrB,MAAM,IAAI,CAACN,QAAQ,CAAC,CAAC;IACrB,MAAM,GAAGO,MAAM,CAAC,GAAG,MAAM,KAAK,CAACJ,OAAO,CAAC,MAAM,EAAEG,SAAS,CAAC;IACzD,OAAO,IAAAI,qBAAa,EAACH,MAAM,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;EACE,MAAMQ,IAAIA,CAAET,SAAS,EAAE;IACrB,MAAM,IAAI,CAACN,QAAQ,CAAC,CAAC;IACrB,MAAM,CAACI,IAAI,CAAC,GAAG,MAAM,KAAK,CAACD,OAAO,CAAC,MAAM,EAAEG,SAAS,CAAC;IACrD,OAAOF,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAMY,IAAIA,CAAA,EAAI;IACZ,MAAM,IAAI,CAAChB,QAAQ,CAAC,CAAC;IACrB,MAAM,CAACI,IAAI,CAAC,GAAG,MAAM,KAAK,CAACD,OAAO,CAAC,MAAM,CAAC;IAC1C,OAAOC,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAMa,IAAIA,CAAA,EAAI;IACZ,MAAM,IAAI,CAACjB,QAAQ,CAAC,CAAC;IACrB,MAAM,CAACI,IAAI,CAAC,GAAG,MAAM,KAAK,CAACD,OAAO,CAAC,MAAM,CAAC;IAC1C;IACA;IACA,OAAOC,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMc,GAAGA,CAAEZ,SAAS,EAAEa,QAAQ,GAAG,CAAC,EAAE;IAClC,MAAM,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACrB,MAAM,GAAGO,MAAM,CAAC,GAAG,MAAM,KAAK,CAACJ,OAAO,CAAC,KAAK,EAAEG,SAAS,EAAEa,QAAQ,CAAC;IAClE,OAAO,IAAAT,qBAAa,EAACH,MAAM,CAAC;EAC9B;;EAEA;AACF;AACA;EACE,MAAMa,IAAIA,CAAA,EAAI;IACZ,IAAI,CAAC,IAAI,CAACnB,OAAO,EAAE;MACjB,IAAI,CAACF,SAAS,GAAG,KAAK;MACtB,OAAO,IAAI,CAACA,SAAS;IACvB;IACA,MAAM,CAACK,IAAI,CAAC,GAAG,MAAM,KAAK,CAACD,OAAO,CAAC,MAAM,CAAC;IAC1C,IAAI,CAACJ,SAAS,GAAGK,IAAI,IAAI,EAAE;IAC3B,OAAO,IAAI,CAACL,SAAS;EACvB;AACF;AAEAX,WAAW,CAACsB,aAAa,GAAGA,qBAAa;AACzCtB,WAAW,CAACoB,OAAO,GAAGA,eAAO;AAAC,IAAAa,QAAA,GAEfjC,WAAW;AAAAkC,OAAA,CAAAnC,OAAA,GAAAkC,QAAA;AAAAE,MAAA,CAAAD,OAAA,GAAAA,OAAA,CAAAnC,OAAA"}