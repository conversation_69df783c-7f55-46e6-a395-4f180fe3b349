{"name": "better-queue", "version": "3.8.12", "description": "Better Queue for NodeJS", "main": "lib/queue.js", "directories": {"test": "test"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/diamondio/better-queue.git"}, "keywords": ["queue", "cargo", "async", "timeout", "priority"], "author": "Diamond Inc. <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/diamondio/better-queue/issues"}, "homepage": "https://github.com/diamondio/better-queue", "devDependencies": {"mocha": "^10.0.0", "mocha-junit-reporter": "^1.12.1"}, "dependencies": {"better-queue-memory": "^1.0.1", "node-eta": "^0.9.0", "uuid": "^9.0.0"}}