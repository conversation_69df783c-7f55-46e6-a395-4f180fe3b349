{"version": 3, "file": "header-generator.js", "sourceRoot": "", "sources": ["../src/header-generator.ts"], "names": [], "mappings": ";;;;AAAA,2BAAkC;AAElC,6EAAqE;AACrE,oDAAoB;AAEpB,2CAWqB;AACrB,mCAKiB;AAEjB,MAAM,yBAAyB,GAAG;IAC9B,IAAI,EAAE,YAAE,CAAC,MAAM;IACf,UAAU,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;IAC9B,UAAU,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;IAC9B,WAAW,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;CAClC,CAAC;AAEW,QAAA,2BAA2B,GAAG;IACvC,QAAQ,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,YAAE,CAAC,GAAG,CAAC,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC,yBAAyB,CAAC,EAAE,YAAE,CAAC,MAAM,CAAC,KAAK,CAAC,8BAAkB,CAAC,CAAC,CAAC;IAChI,gBAAgB,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC,KAAK,CAAC,uCAA2B,CAAC,CAAC;IACxF,OAAO,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC,KAAK,CAAC,6BAAiB,CAAC,CAAC;IACrE,OAAO,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC;IAC5C,WAAW,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAuB,CAAC;IAC9D,gBAAgB,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;IACpC,MAAM,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;CAC9B,CAAC;AA8GF;;EAEE;AACF,MAAa,eAAe;IAqBxB;;MAEE;IACF,YAAY,UAA2C,EAAE;QAvBzD;;;;;WAAsC;QAEtC;;;;;WAAqC;QAE7B;;;;;WAA2B;QAE3B;;;;;WAA4B;QAE5B;;;;;WAAoC;QAEpC;;;;;WAAuB;QAEvB;;;;mBAAgE;gBACpE,SAAS;gBACT,SAAS;gBACT,kBAAkB;gBAClB,UAAU;gBACV,kBAAkB;aACrB;WAAC;QAME,IAAA,YAAE,EAAC,OAAO,EAAE,wBAAwB,EAAE,YAAE,CAAC,MAAM,CAAC,YAAY,CAAC,mCAA2B,CAAC,CAAC,CAAC;QAC3F,iEAAiE;QACjE,MAAM,EACF,QAAQ,GAAG,8BAAkB,EAC7B,gBAAgB,GAAG,uCAA2D,EAC9E,OAAO,GAAG,CAAC,SAAS,CAAC,EACrB,OAAO,GAAG,CAAC,OAAO,CAAC,EACnB,WAAW,GAAG,GAAG,EACjB,gBAAgB,GAAG,EAAE,EACrB,MAAM,GAAG,KAAK,GACjB,GAAG,OAAO,CAAC;QACZ,IAAI,CAAC,aAAa,GAAG;YACjB,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAwB,EAAE,gBAAgB,EAAE,WAAW,CAAC;YAC9F,gBAAgB;YAChB,OAAO;YACP,OAAO;YACP,WAAW;YACX,gBAAgB;YAChB,MAAM;SACT,CAAC;QACF,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,iBAAY,EAAC,GAAG,SAAS,gCAAgC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QACtG,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,iBAAY,EAAC,GAAG,SAAS,sCAAsC,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE7H,KAAK,MAAM,aAAa,IAAI,oBAAoB,EAAE;YAC9C,wHAAwH;YACxH,IAAI,aAAa,KAAK,uCAA2B,EAAE;gBAC/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC,CAAC;aAC1E;SACJ;QAED,IAAI,CAAC,qBAAqB,GAAG,IAAI,6CAAe,CAAC,EAAE,IAAI,EAAE,GAAG,SAAS,0CAA0C,EAAE,CAAC,CAAC;QACnH,IAAI,CAAC,sBAAsB,GAAG,IAAI,6CAAe,CAAC,EAAE,IAAI,EAAE,GAAG,SAAS,2CAA2C,EAAE,CAAC,CAAC;IACzH,CAAC;IAED;;;;;MAKE;IACF,UAAU,CAAC,UAA2C,EAAE,EAAE,0BAAmC,EAAE,EAAE,eAA0B;QACvH,IAAA,YAAE,EAAC,OAAO,EAAE,wBAAwB,EAAE,YAAE,CAAC,MAAM,CAAC,YAAY,CAAC,mCAA2B,CAAC,CAAC,CAAC;QAC3F,MAAM,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,OAAO,EAAE,CAAC;QAC5D,MAAM,uBAAuB,GAAG,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;QAEhF,MAAM,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;YAC3D,mCAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,YAAY,EAAE,eAAe,EAAE,CAAC;YAC1F,mCAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,YAAY,EAAE,eAAe,EAAE,CAAC;SAC7F,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEjB,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC;aAC3D,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC1B,IAAI,GAAG,KAAK,eAAe,EAAE;gBACzB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE;oBAClC,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAChD,IAAI,UAAU,GAAG,gBAAgB,CAAC;oBAElC,IAAI,WAAW,KAAK,GAAG,IAAI,CAAC,gBAAgB,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;wBACxF,UAAU,GAAG,gBAAgB,CAAC;qBACjC;oBAED,OAAO,UAAU,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;gBAClE,CAAC,CAAC,CAAC;gBACH,OAAO,GAAG,CAAC;aACd;YACD,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;YAC/H,OAAO,GAAG,CAAC;QACf,CAAC,EACD,EAAoC,CAAC,CAAC;QAE1C,kHAAkH;QAClH,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,oCAAoC,CAAC,gBAAgB,CAAC,CAAC;QAEtG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,kDAAkD;YAClD,IAAI,aAAa,CAAC,WAAW,KAAK,GAAG,EAAE;gBACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;oBAC7B,GAAG,OAAO;oBACV,WAAW,EAAE,GAAG;iBACnB,EAAE,uBAAuB,EAAE,eAAe,CAAC,CAAC;gBAE7C,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,EAAE;oBAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;wBAChC,OAAO,IAAI,CAAC,CAAC,CAAE,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;oBAChE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACjB,CAAC,CAAC;gBAEF,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CACpC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;oBAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;wBAC9B,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;qBACxB;oBACD,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;wBACtC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;qBACtC;oBACD,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;gBACpC,CAAC,CACA,CAAC,CAAC;gBAEP,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;aAC3C;YAED,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC;YAC5F,IAAI,OAAO,CAAC,MAAM,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;gBAC1C,MAAM,IAAI,KAAK,CAAC,iHAAiH,CAAC,CAAC;aACtI;YAED,uCAAuC;YACvC,MAAM,cAAc,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC5D,OAAO,cAAc,CAAC,aAAa,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,uBAAuB,EAAE,eAAe,CAAC,CAAC;SACpF;QAED,8BAA8B;QAC9B,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAEhF,0FAA0F;QAC1F,MAAM,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,kCAAsB,CAAC,CAAC,CAAC;QACvG,IAAI,sBAAsB,GAA0E,sCAA0B,CAAC;QAC/H,IAAI,uBAAuB,GAAG,iBAAiB,CAAC;QAChD,IAAI,uBAAuB,CAAC,WAAW,KAAK,GAAG,EAAE;YAC7C,uBAAuB,GAAG,iBAAiB,CAAC;YAC5C,sBAAsB,GAAG,sCAA0B,CAAC;SACvD;QAED,eAAe,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAE/F,MAAM,QAAQ,GAAG,uBAAuB,CAAC,IAAI,KAAK,QAAQ,CAAC;QAC3D,MAAM,SAAS,GAAG,uBAAuB,CAAC,IAAI,KAAK,SAAS,CAAC;QAC7D,MAAM,MAAM,GAAG,uBAAuB,CAAC,IAAI,KAAK,MAAM,CAAC;QAEvD,MAAM,WAAW,GAAG,CAAC,QAAQ,IAAI,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;eACvE,CAAC,SAAS,IAAI,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;eACvD,CAAC,MAAM,IAAI,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAExD,8BAA8B;QAC9B,IAAI,WAAW,EAAE;YACb,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;YAC3D,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;YAC1D,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YACpD,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;SAC7D;QAED,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;YAClD,IAAI,SAAS,CAAC,WAAW,EAAE,KAAK,YAAY,IAAI,eAAe,CAAC,SAAS,CAAC,KAAK,OAAO;gBAAE,OAAO,eAAe,CAAC,SAAS,CAAC,CAAC;YAC1H,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,SAAS,CAAC,KAAK,uCAA2B;gBAAE,OAAO,eAAe,CAAC,SAAS,CAAC,CAAC;SAClI;QAED,yDAAyD;QACzD,OAAO,IAAI,CAAC,YAAY,CAAC;YACrB,GAAG,eAAe;YAClB,GAAG,uBAAuB;SAC7B,EAAG,IAAI,CAAC,YAAoC,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;IACjF,CAAC;IAED;;;;MAIE;IACF,YAAY,CAAC,OAAgB,EAAE,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;QACtE,MAAM,aAAa,GAAY,EAAE,CAAC;QAElC,KAAK,MAAM,SAAS,IAAI,KAAK,EAAE;YAC3B,IAAI,SAAS,IAAI,OAAO,EAAE;gBACtB,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;aACjD;SACJ;QAED,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC1C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;gBAC5B,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;aACjD;SACJ;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,sBAAsB,CAC1B,QAAuB,EACvB,gBAAyB,EACzB,WAAyB;QAEzB,IAAI,aAAa,GAAG,QAAQ,CAAC;QAE7B,IAAI,gBAAgB,EAAE;YAClB,aAAa,GAAG,IAAA,4BAAoB,EAAC,gBAAgB,CAAC,CAAC;SAC1D;QAED,OAAO,aAAc,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAClC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBAC7B,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;aACzC;YAED,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;YAClC,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,sBAAsB,CAAC,QAAgC;QAC3D,MAAM,kBAAkB,GAAG,EAAE,CAAC;QAC9B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC7C,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE;oBACrC,MAAM,mBAAmB,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAErD,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,mBAAmB,CAAC;2BAC/D,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,mBAAmB,CAAC;2BAClE,OAAO,CAAC,WAAW,KAAK,aAAa,CAAC,WAAW,EAAE;wBACtD,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;qBACzD;iBACJ;aACJ;SACJ;QAED,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAEO,2BAA2B,CAAC,aAA8C;QAC9E,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG,aAAa,CAAC;QACpG,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,cAAc,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;QAE5F,gDAAgD;QAChD,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QACjE,MAAM,uBAAuB,GAAwB,EAAE,CAAC;QAExD,uBAAuB,CAAC,kCAAsB,CAAC,GAAG,kBAAkB,CAAC;QAErE,uBAAuB,CAAC,sCAA0B,CAAC,GAAG,gBAAgB,CAAC;QAEvE,IAAI,aAAa,CAAC,OAAO,EAAE;YACvB,uBAAuB,CAAC,4BAAgB,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC;SACrE;QAED,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAEO,uBAAuB,CAAC,kBAAqD;QACjF,IAAI,OAAO,GAAG,kBAAkB,CAAC;QACjC,IAAI,gBAAgB,GAAG,EAAE,CAAC;QAC1B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACvB,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACjC;SACJ;QAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACpC,IAAI,0BAA0B,GAAG,KAAK,CAAC;gBACvC,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;oBAC5C,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;wBAClC,0BAA0B,GAAG,IAAI,CAAC;wBAClC,MAAM;qBACT;iBACJ;gBACD,IAAI,CAAC,0BAA0B;oBAAE,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAClE;SACJ;QAED,gBAAgB,GAAG,IAAA,oBAAY,EAAC,gBAAgB,CAAC,CAAC;QAClD,OAAO,GAAG,IAAA,oBAAY,EAAC,OAAO,CAAC,CAAC;QAEhC,MAAM,oBAAoB,GAAG,EAAE,CAAC;QAEhC,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;YAC5C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;gBAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;oBACxE,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACrC;aACJ;YACD,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC9C;QAED,IAAI,wBAAwB,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAEvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClD,wBAAwB,IAAI,IAAI,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;SAC9E;QACD,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;;MAGE;IACM,wBAAwB,CAAC,iBAAyB;QACtD,MAAM,CAAC,aAAa,EAAE,WAAW,CAAC,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClE,IAAI,aAAa,CAAC;QAElB,IAAI,aAAa,KAAK,uCAA2B,EAAE;YAC/C,aAAa,GAAG,EAAE,IAAI,EAAE,uCAA2B,EAAE,CAAC;SACzD;aAAM;YACH,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;SAC5D;QAED,OAAO;YACH,GAAG,aAAa;YAChB,WAAW,EAAE,WAAoD;YACjE,cAAc,EAAE,iBAAiB;SACf,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACK,oBAAoB,CAAC,aAAqB;QAC9C,MAAM,gBAAgB,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpD,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;YACpC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;SACnD;QAED,OAAO;YACH,IAAI,EAAE,gBAAgB,CAAC,CAAC,CAAC;YACzB,OAAO,EAAE,eAAe;YACxB,cAAc,EAAE,aAAa;SACX,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACK,qBAAqB,CAAC,OAA+B;QACzD,MAAM,SAAS,GAAG,IAAA,oBAAY,EAAC,OAAO,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,IAAA,kBAAU,EAAC,SAAS,CAAC,CAAC;QAEtC,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,EAAE,CAAC;SACb;QAED,OAAQ,IAAI,CAAC,YAAoC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IACrE,CAAC;CACJ;AA5WD,0CA4WC"}