{"name": "header-generator", "version": "2.1.62", "description": "NodeJS package for generating realistic browser-like HTTP headers.", "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "homepage": "https://github.com/apify/fingerprint-suite", "license": "Apache-2.0", "engines": {"node": ">=16.0.0"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}}, "dependencies": {"browserslist": "^4.21.1", "generative-bayesian-network": "^2.1.62", "ow": "^0.28.1", "tslib": "^2.4.0"}, "scripts": {"build": "npm run clean && npm run compile", "postbuild": "cp -r src/data_files/  && cp ./README.md ", "clean": "rimraf ./dist", "compile": "tsc -p tsconfig.build.json && gen-esm-wrapper ./index.js ./index.mjs", "copy": "ts-node -T ../../scripts/copy.ts"}, "bugs": {"url": "https://github.com/apify/fingerprint-suite/issues"}, "repository": {"type": "git", "url": "git+https://github.com/apify/fingerprint-suite.git"}}