{"name": "fingerprint-injector", "version": "2.1.62", "description": "Browser fingerprint injection library for Playwright and Puppeteer.", "homepage": "https://github.com/apify/fingerprint-suite", "engines": {"node": ">=16.0.0"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}}, "dependencies": {"fingerprint-generator": "^2.1.62", "tslib": "^2.4.0"}, "peerDependencies": {"playwright": "^1.22.2", "puppeteer": ">= 9.x"}, "peerDependenciesMeta": {"playwright": {"optional": true}, "puppeteer": {"optional": true}}, "scripts": {"build": "npm run clean && npm run compile", "postbuild": "cp ../../README.md ", "clean": "rimraf ./dist", "compile": "tsc -p tsconfig.build.json && gen-esm-wrapper ./index.js ./index.mjs", "copy": "ts-node -T ../../scripts/copy.ts"}, "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/fingerprint-suite.git"}}