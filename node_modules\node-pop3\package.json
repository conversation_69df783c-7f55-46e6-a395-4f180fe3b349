{"name": "node-pop3", "version": "0.9.0", "description": "POP3 client for node", "main": "./lib/Command.cjs", "type": "module", "types": "./dist/src/Command.d.ts", "exports": {"types": "./dist/src/Command.d.ts", "import": "./src/Command.js", "require": "./lib/Command.cjs"}, "bin": {"pop": "./bin/pop.js"}, "c8": {"reporter": ["text", "lcov"]}, "repository": {"type": "git", "url": "git+https://github.com/lianxh/node-pop3.git"}, "keywords": ["pop3", "node", "Promise", "Stream"], "author": "lianxh", "contributors": ["<PERSON>"], "license": "MIT", "bugs": {"url": "https://github.com/lianxh/node-pop3/issues"}, "homepage": "https://github.com/lianxh/node-pop3#readme", "engines": {"node": ">=14.13.0"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "@expo/spawn-async": "^1.7.2", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "@types/node": "^20.2.3", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-module-extension": "^0.1.3", "c8": "^7.13.0", "chai": "^4.3.7", "emailjs": "^4.0.2", "eslint": "^8.41.0", "eslint-config-ash-nazg": "^34.13.0", "mocha": "^10.2.0"}, "scripts": {"tsc": "tsc", "build": "tsc -p tsconfig-prod.json", "cli": "node ./bin/pop.js", "lint": "npm run eslint", "eslint": "eslint .", "babel": "babel src -d lib --source-maps --out-file-extension .cjs", "mocha": "mocha --require chai/register-expect.js --exit test/*.js", "test": "rm -Rf .nyc_output && rm -Rf ./node_modules/.cache && npm run babel && c8 npm run mocha"}}