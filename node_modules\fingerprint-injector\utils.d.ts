declare function getSlim(): any;
/**
 * @param masterObject Object to override.
 * @param propertyName Property to override.
 * @param proxyHandler Proxy handled with the new value.
 */
declare function overridePropertyWithProxy(masterObject: any, propertyName: any, proxyHandler: any): void;
declare function useStrictModeExceptions(prop: any): void;
/**
 * @param masterObject Object to override.
 * @param propertyName Property to override.
 * @param proxyHandler ES6 Proxy handler object with a get handle only.
 */
declare function overrideGetterWithProxy(masterObject: any, propertyName: any, proxyHandler: any): void;
/**
 * @param instance Instance to override.
 * @param overrideObj New instance values.
 */
declare function overrideInstancePrototype(instance: any, overrideObj: any): void;
/**
 * Updates the .toString method in Function.prototype to return a native string representation of the function.
 * @param {*} proxyObj
 * @param {*} originalObj
 */
declare function redirectToString(proxyObj: any, originalObj: any): void;
declare function makeNativeString(name?: string): string;
declare function redefineProperty(masterObject: any, propertyName: any, descriptorOverrides?: {}): any;
/**
 * For all the traps in the passed proxy handler, we wrap them in a try/catch and modify the error stack if they throw.
 * @param {*} handler A proxy handler object
 * @returns A new proxy handler object with error stack modifications
 */
declare function stripProxyFromErrors(handler: any): {};
declare function overrideWebGl(webGl: any): void;
declare function overrideBattery(batteryInfo: any): void;
declare function overrideIntlAPI(language: any): void;
declare function makeHandler(): {
    getterValue: (value: any) => {
        apply(target: any, ctx: any, args: any, ...args: any[]): any;
        get: (target: any, prop: any, receiver: any, ...args: any[]) => any;
    };
};
declare function overrideScreenByReassigning(target: any, newProperties: any): void;
declare function overrideWindowDimensionsProps(props: any): void;
declare function overrideDocumentDimensionsProps(props: any): void;
declare function replace(target: any, key: any, value: any): void;
declare function blockWebRTC(): void;
declare function overrideUserAgentData(userAgentData: any): void;
declare function fixWindowChrome(): void;
declare function fixPermissions(): void;
declare function fixIframeContentWindow(): void;
declare function fixPluginArray(): void;
declare function runHeadlessFixes(): void;
declare function overrideStatic(): void;
declare const isHeadlessChromium: boolean;
declare const isChrome: boolean;
declare const isFirefox: boolean;
declare const isSafari: boolean;
declare let slim: any;
declare namespace cache {
    namespace Reflect {
        let get: typeof Reflect.get;
        let apply: typeof Reflect.apply;
    }
    let nativeToStringStr: string;
}
declare namespace prototypeProxyHandler {
    function setPrototypeOf(target: any, newProto: any): boolean;
}
declare function overrideCodecs(audioCodecs: any, videoCodecs: any): void;
//# sourceMappingURL=utils.d.ts.map