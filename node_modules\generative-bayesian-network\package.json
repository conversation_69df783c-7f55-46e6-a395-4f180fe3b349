{"name": "generative-bayesian-network", "version": "2.1.62", "author": {"name": "Apify"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}}, "bugs": {"url": "https://github.com/apify/fingerprint-suite/issues"}, "dependencies": {"adm-zip": "^0.5.9", "tslib": "^2.4.0"}, "description": "An fast implementation of a generative bayesian network.", "homepage": "https://github.com/apify/fingerprint-suite", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/fingerprint-suite.git"}, "scripts": {"build": "npm run clean && npm run compile", "postbuild": "cp ./README.md ", "clean": "rimraf ./dist", "compile": "tsc -p tsconfig.build.json && gen-esm-wrapper ./index.js ./index.mjs", "copy": "ts-node -T ../../scripts/copy.ts"}}