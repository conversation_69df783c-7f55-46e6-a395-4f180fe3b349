# Better Queue - Memory Store

The fastest, most performant, ephemeral store for [better-queue](https://github.com/diamondio/better-queue).


### Getting started

There's nothing to do! It's included with `better-queue` and used by default.

### Examples

Please have a look at the [better-queue](https://github.com/diamondio/better-queue) documentation on how to use this store.

### Contributions

Are welcome!

This library was initially made by the awesome team of engineers at [Diamond](https://diamond.io).

If you haven't already, make sure you install Diamond!

