"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.utils = exports.BayesianNetwork = void 0;
const tslib_1 = require("tslib");
var bayesian_network_1 = require("./bayesian-network");
Object.defineProperty(exports, "BayesianNetwork", { enumerable: true, get: function () { return bayesian_network_1.BayesianNetwork; } });
exports.utils = tslib_1.__importStar(require("./utils"));
//# sourceMappingURL=index.js.map