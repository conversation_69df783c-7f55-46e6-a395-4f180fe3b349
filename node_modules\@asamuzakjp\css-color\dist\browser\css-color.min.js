var sl=Object.defineProperty,to=t=>{throw TypeError(t)},al=(t,e,n)=>e in t?sl(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,K=(t,e,n)=>al(t,typeof e!="symbol"?e+"":e,n),_s=(t,e,n)=>e.has(t)||to("Cannot "+n),u=(t,e,n)=>(_s(t,e,"read from private field"),n?n.call(t):e.get(t)),z=(t,e,n)=>e.has(t)?to("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),C=(t,e,n,r)=>(_s(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),L=(t,e,n)=>(_s(t,e,"access private method"),n),Hs=(t,e,n,r)=>({set _(s){C(t,e,s,n)},get _(){return u(t,e,r)}});class dr extends Error{constructor(e,n,r,s){super(e),K(this,"sourceStart"),K(this,"sourceEnd"),K(this,"parserState"),this.name="ParseError",this.sourceStart=n,this.sourceEnd=r,this.parserState=s}}class an extends dr{constructor(e,n,r,s,a){super(e,n,r,s),K(this,"token"),this.token=a}}const qe={UnexpectedNewLineInString:"Unexpected newline while consuming a string token.",UnexpectedEOFInString:"Unexpected EOF while consuming a string token.",UnexpectedEOFInComment:"Unexpected EOF while consuming a comment.",UnexpectedEOFInURL:"Unexpected EOF while consuming a url token.",UnexpectedEOFInEscapedCodePoint:"Unexpected EOF while consuming an escaped code point.",UnexpectedCharacterInURL:"Unexpected character while consuming a url token.",InvalidEscapeSequenceInURL:"Invalid escape sequence while consuming a url token.",InvalidEscapeSequenceAfterBackslash:'Invalid escape sequence after "\\"'};function be(...t){let e="";for(let n=0;n<t.length;n++)e+=t[n][1];return e}const Mn=13,St=45,Dn=10,Bn=43,Rn=65533;function ol(t){return t.source.codePointAt(t.cursor)===60&&t.source.codePointAt(t.cursor+1)===33&&t.source.codePointAt(t.cursor+2)===St&&t.source.codePointAt(t.cursor+3)===St}function ee(t){return t>=48&&t<=57}function il(t){return t>=65&&t<=90}function ll(t){return t>=97&&t<=122}function bn(t){return t>=48&&t<=57||t>=97&&t<=102||t>=65&&t<=70}function cl(t){return ll(t)||il(t)}function On(t){return cl(t)||ul(t)||t===95}function Us(t){return On(t)||ee(t)||t===St}function ul(t){return t===183||t===8204||t===8205||t===8255||t===8256||t===8204||192<=t&&t<=214||216<=t&&t<=246||248<=t&&t<=893||895<=t&&t<=8191||8304<=t&&t<=8591||11264<=t&&t<=12271||12289<=t&&t<=55295||63744<=t&&t<=64975||65008<=t&&t<=65533||t===0||!!Wn(t)||t>=65536}function Xr(t){return t===Dn||t===Mn||t===12}function wn(t){return t===32||t===Dn||t===9||t===Mn||t===12}function Wn(t){return t>=55296&&t<=57343}function Tn(t){return t.source.codePointAt(t.cursor)===92&&!Xr(t.source.codePointAt(t.cursor+1)??-1)}function Zr(t,e){return e.source.codePointAt(e.cursor)===St?e.source.codePointAt(e.cursor+1)===St||!!On(e.source.codePointAt(e.cursor+1)??-1)||e.source.codePointAt(e.cursor+1)===92&&!Xr(e.source.codePointAt(e.cursor+2)??-1):!!On(e.source.codePointAt(e.cursor)??-1)||Tn(e)}function eo(t){return t.source.codePointAt(t.cursor)===Bn||t.source.codePointAt(t.cursor)===St?!!ee(t.source.codePointAt(t.cursor+1)??-1)||t.source.codePointAt(t.cursor+1)===46&&ee(t.source.codePointAt(t.cursor+2)??-1):t.source.codePointAt(t.cursor)===46?ee(t.source.codePointAt(t.cursor+1)??-1):ee(t.source.codePointAt(t.cursor)??-1)}function hl(t){return t.source.codePointAt(t.cursor)===47&&t.source.codePointAt(t.cursor+1)===42}function fl(t){return t.source.codePointAt(t.cursor)===St&&t.source.codePointAt(t.cursor+1)===St&&t.source.codePointAt(t.cursor+2)===62}var v,x,Yr;function pl(t){switch(t){case v.OpenParen:return v.CloseParen;case v.CloseParen:return v.OpenParen;case v.OpenCurly:return v.CloseCurly;case v.CloseCurly:return v.OpenCurly;case v.OpenSquare:return v.CloseSquare;case v.CloseSquare:return v.OpenSquare;default:return null}}function dl(t){switch(t[0]){case v.OpenParen:return[v.CloseParen,")",-1,-1,void 0];case v.CloseParen:return[v.OpenParen,"(",-1,-1,void 0];case v.OpenCurly:return[v.CloseCurly,"}",-1,-1,void 0];case v.CloseCurly:return[v.OpenCurly,"{",-1,-1,void 0];case v.OpenSquare:return[v.CloseSquare,"]",-1,-1,void 0];case v.CloseSquare:return[v.OpenSquare,"[",-1,-1,void 0];default:return null}}function ml(t,e){for(e.advanceCodePoint(2);;){const n=e.readCodePoint();if(n===void 0){const r=[v.Comment,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new an(qe.UnexpectedEOFInComment,e.representationStart,e.representationEnd,["4.3.2. Consume comments","Unexpected EOF"],r)),r}if(n===42&&e.source.codePointAt(e.cursor)!==void 0&&e.source.codePointAt(e.cursor)===47){e.advanceCodePoint();break}}return[v.Comment,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0]}function Qr(t,e){const n=e.readCodePoint();if(n===void 0)return t.onParseError(new dr(qe.UnexpectedEOFInEscapedCodePoint,e.representationStart,e.representationEnd,["4.3.7. Consume an escaped code point","Unexpected EOF"])),Rn;if(bn(n)){const r=[n];let s;for(;(s=e.source.codePointAt(e.cursor))!==void 0&&bn(s)&&r.length<6;)r.push(s),e.advanceCodePoint();wn(e.source.codePointAt(e.cursor)??-1)&&(e.source.codePointAt(e.cursor)===Mn&&e.source.codePointAt(e.cursor+1)===Dn&&e.advanceCodePoint(),e.advanceCodePoint());const a=parseInt(String.fromCodePoint(...r),16);return a===0||Wn(a)||a>1114111?Rn:a}return n===0||Wn(n)?Rn:n}function Jr(t,e){const n=[];for(;;){const r=e.source.codePointAt(e.cursor)??-1;if(r===0||Wn(r))n.push(Rn),e.advanceCodePoint(+(r>65535)+1);else if(Us(r))n.push(r),e.advanceCodePoint(+(r>65535)+1);else{if(!Tn(e))return n;e.advanceCodePoint(),n.push(Qr(t,e))}}}function gl(t,e){e.advanceCodePoint();const n=e.source.codePointAt(e.cursor);if(n!==void 0&&(Us(n)||Tn(e))){let r=Yr.Unrestricted;Zr(0,e)&&(r=Yr.ID);const s=Jr(t,e);return[v.Hash,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...s),type:r}]}return[v.Delim,"#",e.representationStart,e.representationEnd,{value:"#"}]}function vl(t,e){let n=x.Integer;for(e.source.codePointAt(e.cursor)!==Bn&&e.source.codePointAt(e.cursor)!==St||e.advanceCodePoint();ee(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===46&&ee(e.source.codePointAt(e.cursor+1)??-1))for(e.advanceCodePoint(2),n=x.Number;ee(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===101||e.source.codePointAt(e.cursor)===69){if(ee(e.source.codePointAt(e.cursor+1)??-1))e.advanceCodePoint(2);else{if(e.source.codePointAt(e.cursor+1)!==St&&e.source.codePointAt(e.cursor+1)!==Bn||!ee(e.source.codePointAt(e.cursor+2)??-1))return n;e.advanceCodePoint(3)}for(n=x.Number;ee(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint()}return n}function zs(t,e){let n;{const a=e.source.codePointAt(e.cursor);a===St?n="-":a===Bn&&(n="+")}const r=vl(0,e),s=parseFloat(e.source.slice(e.representationStart,e.representationEnd+1));if(Zr(0,e)){const a=Jr(t,e);return[v.Dimension,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n,type:r,unit:String.fromCodePoint(...a)}]}return e.source.codePointAt(e.cursor)===37?(e.advanceCodePoint(),[v.Percentage,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n}]):[v.Number,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n,type:r}]}function bl(t){for(;wn(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();return[v.Whitespace,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0]}(function(t){t.Comment="comment",t.AtKeyword="at-keyword-token",t.BadString="bad-string-token",t.BadURL="bad-url-token",t.CDC="CDC-token",t.CDO="CDO-token",t.Colon="colon-token",t.Comma="comma-token",t.Delim="delim-token",t.Dimension="dimension-token",t.EOF="EOF-token",t.Function="function-token",t.Hash="hash-token",t.Ident="ident-token",t.Number="number-token",t.Percentage="percentage-token",t.Semicolon="semicolon-token",t.String="string-token",t.URL="url-token",t.Whitespace="whitespace-token",t.OpenParen="(-token",t.CloseParen=")-token",t.OpenSquare="[-token",t.CloseSquare="]-token",t.OpenCurly="{-token",t.CloseCurly="}-token",t.UnicodeRange="unicode-range-token"})(v||(v={})),function(t){t.Integer="integer",t.Number="number"}(x||(x={})),function(t){t.Unrestricted="unrestricted",t.ID="id"}(Yr||(Yr={}));class wl{constructor(e){K(this,"cursor",0),K(this,"source",""),K(this,"representationStart",0),K(this,"representationEnd",-1),this.source=e}advanceCodePoint(e=1){this.cursor=this.cursor+e,this.representationEnd=this.cursor-1}readCodePoint(){const e=this.source.codePointAt(this.cursor);if(e!==void 0)return this.cursor=this.cursor+1,this.representationEnd=this.cursor-1,e}unreadCodePoint(e=1){this.cursor=this.cursor-e,this.representationEnd=this.cursor-1}resetRepresentation(){this.representationStart=this.cursor,this.representationEnd=-1}}function $l(t,e){let n="";const r=e.readCodePoint();for(;;){const s=e.readCodePoint();if(s===void 0){const a=[v.String,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new an(qe.UnexpectedEOFInString,e.representationStart,e.representationEnd,["4.3.5. Consume a string token","Unexpected EOF"],a)),a}if(Xr(s)){e.unreadCodePoint();const a=[v.BadString,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new an(qe.UnexpectedNewLineInString,e.representationStart,e.source.codePointAt(e.cursor)===Mn&&e.source.codePointAt(e.cursor+1)===Dn?e.representationEnd+2:e.representationEnd+1,["4.3.5. Consume a string token","Unexpected newline"],a)),a}if(s===r)return[v.String,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];if(s!==92)s===0||Wn(s)?n+=String.fromCodePoint(Rn):n+=String.fromCodePoint(s);else{if(e.source.codePointAt(e.cursor)===void 0)continue;if(Xr(e.source.codePointAt(e.cursor)??-1)){e.source.codePointAt(e.cursor)===Mn&&e.source.codePointAt(e.cursor+1)===Dn&&e.advanceCodePoint(),e.advanceCodePoint();continue}n+=String.fromCodePoint(Qr(t,e))}}}function yl(t){return!(t.length!==3||t[0]!==117&&t[0]!==85||t[1]!==114&&t[1]!==82||t[2]!==108&&t[2]!==76)}function Gs(t,e){for(;;){const n=e.source.codePointAt(e.cursor);if(n===void 0)return;if(n===41)return void e.advanceCodePoint();Tn(e)?(e.advanceCodePoint(),Qr(t,e)):e.advanceCodePoint()}}function Nl(t,e){for(;wn(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();let n="";for(;;){if(e.source.codePointAt(e.cursor)===void 0){const a=[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new an(qe.UnexpectedEOFInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","Unexpected EOF"],a)),a}if(e.source.codePointAt(e.cursor)===41)return e.advanceCodePoint(),[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];if(wn(e.source.codePointAt(e.cursor)??-1)){for(e.advanceCodePoint();wn(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===void 0){const a=[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new an(qe.UnexpectedEOFInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","Consume as much whitespace as possible","Unexpected EOF"],a)),a}return e.source.codePointAt(e.cursor)===41?(e.advanceCodePoint(),[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}]):(Gs(t,e),[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0])}const s=e.source.codePointAt(e.cursor);if(s===34||s===39||s===40||(r=s??-1)===11||r===127||0<=r&&r<=8||14<=r&&r<=31){Gs(t,e);const a=[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new an(qe.UnexpectedCharacterInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token",`Unexpected U+0022 QUOTATION MARK ("), U+0027 APOSTROPHE ('), U+0028 LEFT PARENTHESIS (() or non-printable code point`],a)),a}if(s===92){if(Tn(e)){e.advanceCodePoint(),n+=String.fromCodePoint(Qr(t,e));continue}Gs(t,e);const a=[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new an(qe.InvalidEscapeSequenceInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],a)),a}e.source.codePointAt(e.cursor)===0||Wn(e.source.codePointAt(e.cursor)??-1)?(n+=String.fromCodePoint(Rn),e.advanceCodePoint()):(n+=e.source[e.cursor],e.advanceCodePoint())}var r}function js(t,e){const n=Jr(t,e);if(e.source.codePointAt(e.cursor)!==40)return[v.Ident,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}];if(yl(n)){e.advanceCodePoint();let r=0;for(;;){const s=wn(e.source.codePointAt(e.cursor)??-1),a=wn(e.source.codePointAt(e.cursor+1)??-1);if(s&&a){r+=1,e.advanceCodePoint(1);continue}const o=s?e.source.codePointAt(e.cursor+1):e.source.codePointAt(e.cursor);if(o===34||o===39)return r>0&&e.unreadCodePoint(r),[v.Function,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}];break}return Nl(t,e)}return e.advanceCodePoint(),[v.Function,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}]}function El(t){return!(t.source.codePointAt(t.cursor)!==117&&t.source.codePointAt(t.cursor)!==85||t.source.codePointAt(t.cursor+1)!==Bn||t.source.codePointAt(t.cursor+2)!==63&&!bn(t.source.codePointAt(t.cursor+2)??-1))}function Cl(t,e){e.advanceCodePoint(2);const n=[],r=[];let s;for(;(s=e.source.codePointAt(e.cursor))!==void 0&&n.length<6&&bn(s);)n.push(s),e.advanceCodePoint();for(;(s=e.source.codePointAt(e.cursor))!==void 0&&n.length<6&&s===63;)r.length===0&&r.push(...n),n.push(48),r.push(70),e.advanceCodePoint();if(!r.length&&e.source.codePointAt(e.cursor)===St&&bn(e.source.codePointAt(e.cursor+1)??-1))for(e.advanceCodePoint();(s=e.source.codePointAt(e.cursor))!==void 0&&r.length<6&&bn(s);)r.push(s),e.advanceCodePoint();if(!r.length){const i=parseInt(String.fromCodePoint(...n),16);return[v.UnicodeRange,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{startOfRange:i,endOfRange:i}]}const a=parseInt(String.fromCodePoint(...n),16),o=parseInt(String.fromCodePoint(...r),16);return[v.UnicodeRange,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{startOfRange:a,endOfRange:o}]}function Ve(t,e){const n=no(t),r=[];for(;!n.endOfFile();)r.push(n.nextToken());return r.push(n.nextToken()),r}function no(t,e){const n=t.css.valueOf(),r=t.unicodeRangesAllowed??!1,s=new wl(n),a={onParseError:kl};return{nextToken:function(){s.resetRepresentation();const o=s.source.codePointAt(s.cursor);if(o===void 0)return[v.EOF,"",-1,-1,void 0];if(o===47&&hl(s))return ml(a,s);if(r&&(o===117||o===85)&&El(s))return Cl(0,s);if(On(o))return js(a,s);if(ee(o))return zs(a,s);switch(o){case 44:return s.advanceCodePoint(),[v.Comma,",",s.representationStart,s.representationEnd,void 0];case 58:return s.advanceCodePoint(),[v.Colon,":",s.representationStart,s.representationEnd,void 0];case 59:return s.advanceCodePoint(),[v.Semicolon,";",s.representationStart,s.representationEnd,void 0];case 40:return s.advanceCodePoint(),[v.OpenParen,"(",s.representationStart,s.representationEnd,void 0];case 41:return s.advanceCodePoint(),[v.CloseParen,")",s.representationStart,s.representationEnd,void 0];case 91:return s.advanceCodePoint(),[v.OpenSquare,"[",s.representationStart,s.representationEnd,void 0];case 93:return s.advanceCodePoint(),[v.CloseSquare,"]",s.representationStart,s.representationEnd,void 0];case 123:return s.advanceCodePoint(),[v.OpenCurly,"{",s.representationStart,s.representationEnd,void 0];case 125:return s.advanceCodePoint(),[v.CloseCurly,"}",s.representationStart,s.representationEnd,void 0];case 39:case 34:return $l(a,s);case 35:return gl(a,s);case Bn:case 46:return eo(s)?zs(a,s):(s.advanceCodePoint(),[v.Delim,s.source[s.representationStart],s.representationStart,s.representationEnd,{value:s.source[s.representationStart]}]);case Dn:case Mn:case 12:case 9:case 32:return bl(s);case St:return eo(s)?zs(a,s):fl(s)?(s.advanceCodePoint(3),[v.CDC,"-->",s.representationStart,s.representationEnd,void 0]):Zr(0,s)?js(a,s):(s.advanceCodePoint(),[v.Delim,"-",s.representationStart,s.representationEnd,{value:"-"}]);case 60:return ol(s)?(s.advanceCodePoint(4),[v.CDO,"<!--",s.representationStart,s.representationEnd,void 0]):(s.advanceCodePoint(),[v.Delim,"<",s.representationStart,s.representationEnd,{value:"<"}]);case 64:if(s.advanceCodePoint(),Zr(0,s)){const i=Jr(a,s);return[v.AtKeyword,s.source.slice(s.representationStart,s.representationEnd+1),s.representationStart,s.representationEnd,{value:String.fromCodePoint(...i)}]}return[v.Delim,"@",s.representationStart,s.representationEnd,{value:"@"}];case 92:{if(Tn(s))return js(a,s);s.advanceCodePoint();const i=[v.Delim,"\\",s.representationStart,s.representationEnd,{value:"\\"}];return a.onParseError(new an(qe.InvalidEscapeSequenceAfterBackslash,s.representationStart,s.representationEnd,["4.3.1. Consume a token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],i)),i}}return s.advanceCodePoint(),[v.Delim,s.source[s.representationStart],s.representationStart,s.representationEnd,{value:s.source[s.representationStart]}]},endOfFile:function(){return s.source.codePointAt(s.cursor)===void 0}}}function kl(){}function ro(t,e){const n=[];for(const i of e)n.push(i.codePointAt(0));const r=Fl(n);r[0]===101&&ts(r,0,r[0]);const s=String.fromCodePoint(...r),a=t[4].signCharacter==="+"?t[4].signCharacter:"",o=t[4].value.toString();t[1]=`${a}${o}${s}`,t[4].unit=e}function Fl(t){let e=0;t[0]===St&&t[1]===St?e=2:t[0]===St&&t[1]?(e=2,On(t[1])||(e+=ts(t,1,t[1]))):On(t[0])?e=1:(e=1,e+=ts(t,0,t[0]));for(let n=e;n<t.length;n++)Us(t[n])||(n+=ts(t,n,t[n]));return t}function ts(t,e,n){const r=n.toString(16),s=[];for(const o of r)s.push(o.codePointAt(0));const a=t[e+1];return e===t.length-1||a&&bn(a)?(t.splice(e,1,92,...s,32),1+s.length):(t.splice(e,1,92,...s),s.length)}const xl=Object.values(v);function qs(t){return!!Array.isArray(t)&&!(t.length<4)&&!!xl.includes(t[0])&&typeof t[1]=="string"&&typeof t[2]=="number"&&typeof t[3]=="number"}function pt(t){if(!t)return!1;switch(t[0]){case v.Dimension:case v.Number:case v.Percentage:return!0;default:return!1}}function so(t){if(!t)return!1;switch(t[0]){case v.Whitespace:case v.Comment:return!0;default:return!1}}function ne(t){return!!t&&t[0]===v.Comma}function ao(t){return!!t&&t[0]===v.Comment}function es(t){return!!t&&t[0]===v.Delim}function nt(t){return!!t&&t[0]===v.Dimension}function Se(t){return!!t&&t[0]===v.EOF}function Sl(t){return!!t&&t[0]===v.Function}function Al(t){return!!t&&t[0]===v.Hash}function vt(t){return!!t&&t[0]===v.Ident}function H(t){return!!t&&t[0]===v.Number}function tt(t){return!!t&&t[0]===v.Percentage}function Vs(t){return!!t&&t[0]===v.Whitespace}function oo(t){return!!t&&t[0]===v.OpenParen}function Pl(t){return!!t&&t[0]===v.CloseParen}function Ml(t){return!!t&&t[0]===v.OpenSquare}function Dl(t){return!!t&&t[0]===v.OpenCurly}var re;function io(t){let e=t.slice();return(n,r,s)=>{let a=-1;for(let o=e.indexOf(r);o<e.length&&(a=n.indexOf(e[o]),a===-1||a<s);o++);return a===-1||a===s&&r===n[s]&&(a++,a>=n.length)?-1:(e=n.slice(),a)}}function ns(t,e){const n=e[0];if(oo(n)||Dl(n)||Ml(n)){const r=Rl(t,e);return{advance:r.advance,node:r.node}}if(Sl(n)){const r=Bl(t,e);return{advance:r.advance,node:r.node}}if(Vs(n)){const r=co(t,e);return{advance:r.advance,node:r.node}}if(ao(n)){const r=Ol(t,e);return{advance:r.advance,node:r.node}}return{advance:1,node:new G(n)}}(function(t){t.Function="function",t.SimpleBlock="simple-block",t.Whitespace="whitespace",t.Comment="comment",t.Token="token"})(re||(re={}));class lo{constructor(){K(this,"value",[])}indexOf(e){return this.value.indexOf(e)}at(e){if(typeof e=="number")return e<0&&(e=this.value.length+e),this.value[e]}forEach(e,n){if(this.value.length===0)return;const r=io(this.value);let s=0;for(;s<this.value.length;){const a=this.value[s];let o;if(n&&(o={...n}),e({node:a,parent:this,state:o},s)===!1)return!1;if(s=r(this.value,a,s),s===-1)break}}walk(e,n){this.value.length!==0&&this.forEach((r,s)=>e(r,s)!==!1&&(!("walk"in r.node)||!this.value.includes(r.node)||r.node.walk(e,r.state)!==!1)&&void 0,n)}}class _t extends lo{constructor(e,n,r){super(),K(this,"type",re.Function),K(this,"name"),K(this,"endToken"),this.name=e,this.endToken=n,this.value=r}getName(){return this.name[4].value}normalize(){Se(this.endToken)&&(this.endToken=[v.CloseParen,")",-1,-1,void 0])}tokens(){return Se(this.endToken)?[this.name,...this.value.flatMap(e=>e.tokens())]:[this.name,...this.value.flatMap(e=>e.tokens()),this.endToken]}toString(){const e=this.value.map(n=>qs(n)?be(n):n.toString()).join("");return be(this.name)+e+be(this.endToken)}toJSON(){return{type:this.type,name:this.getName(),tokens:this.tokens(),value:this.value.map(e=>e.toJSON())}}isFunctionNode(){return _t.isFunctionNode(this)}static isFunctionNode(e){return!!e&&e instanceof _t&&e.type===re.Function}}function Bl(t,e){const n=[];let r=1;for(;;){const s=e[r];if(!s||Se(s))return t.onParseError(new dr("Unexpected EOF while consuming a function.",e[0][2],e[e.length-1][3],["5.4.9. Consume a function","Unexpected EOF"])),{advance:e.length,node:new _t(e[0],s,n)};if(Pl(s))return{advance:r+1,node:new _t(e[0],s,n)};if(so(s)){const o=uo(t,e.slice(r));r+=o.advance,n.push(...o.nodes);continue}const a=ns(t,e.slice(r));r+=a.advance,n.push(a.node)}}class fr extends lo{constructor(e,n,r){super(),K(this,"type",re.SimpleBlock),K(this,"startToken"),K(this,"endToken"),this.startToken=e,this.endToken=n,this.value=r}normalize(){if(Se(this.endToken)){const e=dl(this.startToken);e&&(this.endToken=e)}}tokens(){return Se(this.endToken)?[this.startToken,...this.value.flatMap(e=>e.tokens())]:[this.startToken,...this.value.flatMap(e=>e.tokens()),this.endToken]}toString(){const e=this.value.map(n=>qs(n)?be(n):n.toString()).join("");return be(this.startToken)+e+be(this.endToken)}toJSON(){return{type:this.type,startToken:this.startToken,tokens:this.tokens(),value:this.value.map(e=>e.toJSON())}}isSimpleBlockNode(){return fr.isSimpleBlockNode(this)}static isSimpleBlockNode(e){return!!e&&e instanceof fr&&e.type===re.SimpleBlock}}function Rl(t,e){const n=pl(e[0][0]);if(!n)throw new Error("Failed to parse, a mirror variant must exist for all block open tokens.");const r=[];let s=1;for(;;){const a=e[s];if(!a||Se(a))return t.onParseError(new dr("Unexpected EOF while consuming a simple block.",e[0][2],e[e.length-1][3],["5.4.8. Consume a simple block","Unexpected EOF"])),{advance:e.length,node:new fr(e[0],a,r)};if(a[0]===n)return{advance:s+1,node:new fr(e[0],a,r)};if(so(a)){const i=uo(t,e.slice(s));s+=i.advance,r.push(...i.nodes);continue}const o=ns(t,e.slice(s));s+=o.advance,r.push(o.node)}}class te{constructor(e){K(this,"type",re.Whitespace),K(this,"value"),this.value=e}tokens(){return this.value}toString(){return be(...this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isWhitespaceNode(){return te.isWhitespaceNode(this)}static isWhitespaceNode(e){return!!e&&e instanceof te&&e.type===re.Whitespace}}function co(t,e){let n=0;for(;;){const r=e[n];if(!Vs(r))return{advance:n,node:new te(e.slice(0,n))};n++}}class pr{constructor(e){K(this,"type",re.Comment),K(this,"value"),this.value=e}tokens(){return[this.value]}toString(){return be(this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isCommentNode(){return pr.isCommentNode(this)}static isCommentNode(e){return!!e&&e instanceof pr&&e.type===re.Comment}}function Ol(t,e){return{advance:1,node:new pr(e[0])}}function uo(t,e){const n=[];let r=0;for(;;)if(Vs(e[r])){const s=co(0,e.slice(r));r+=s.advance,n.push(s.node)}else{if(!ao(e[r]))return{advance:r,nodes:n};n.push(new pr(e[r])),r++}}class G{constructor(e){K(this,"type",re.Token),K(this,"value"),this.value=e}tokens(){return[this.value]}toString(){return this.value[1]}toJSON(){return{type:this.type,tokens:this.tokens()}}isTokenNode(){return G.isTokenNode(this)}static isTokenNode(e){return!!e&&e instanceof G&&e.type===re.Token}}function Wl(t,e){const n={onParseError:()=>{}},r=[...t];Se(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const s=ns(n,r);if(Se(r[Math.min(s.advance,r.length-1)]))return s.node;n.onParseError(new dr("Expected EOF after parsing a component value.",t[0][2],t[t.length-1][3],["5.3.9. Parse a component value","Expected EOF"]))}function Tl(t,e){const n={onParseError:e?.onParseError??(()=>{})},r=[...t];if(t.length===0)return[];Se(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const s=[];let a=[],o=0;for(;;){if(!r[o]||Se(r[o]))return a.length&&s.push(a),s;if(ne(r[o])){s.push(a),a=[],o++;continue}const i=ns(n,t.slice(o));a.push(i.node),o+=i.advance}}function Ll(t,e,n){if(t.length===0)return;const r=io(t);let s=0;for(;s<t.length;){const a=t[s];if(e({node:a,parent:{value:t},state:void 0},s)===!1)return!1;if(s=r(t,a,s),s===-1)break}}function Il(t,e,n){t.length!==0&&Ll(t,(r,s)=>e(r,s)!==!1&&(!("walk"in r.node)||!t.includes(r.node)||r.node.walk(e,r.state)!==!1)&&void 0)}function _l(t,e){for(let n=0;n<t.length;n++)Il(t[n],(r,s)=>{if(typeof s!="number")return;const a=e(r.node);a&&(Array.isArray(a)?r.parent.value.splice(s,1,...a):r.parent.value.splice(s,1,a))});return t}function Hl(t){return fr.isSimpleBlockNode(t)}function se(t){return _t.isFunctionNode(t)}function ae(t){return te.isWhitespaceNode(t)}function oe(t){return pr.isCommentNode(t)}function $n(t){return ae(t)||oe(t)}function I(t){return G.isTokenNode(t)}const Ul=/[A-Z]/g;function Ht(t){return t.replace(Ul,e=>String.fromCharCode(e.charCodeAt(0)+32))}const zl={cm:"px",in:"px",mm:"px",pc:"px",pt:"px",px:"px",q:"px",deg:"deg",grad:"deg",rad:"deg",turn:"deg",ms:"s",s:"s",hz:"hz",khz:"hz"},Gl=new Map([["cm",t=>t],["mm",t=>10*t],["q",t=>40*t],["in",t=>t/2.54],["pc",t=>t/2.54*6],["pt",t=>t/2.54*72],["px",t=>t/2.54*96]]),rs=new Map([["deg",t=>t],["grad",t=>t/.9],["rad",t=>t/180*Math.PI],["turn",t=>t/360]]),mr=new Map([["deg",t=>.9*t],["grad",t=>t],["rad",t=>.9*t/180*Math.PI],["turn",t=>.9*t/360]]),jl=new Map([["hz",t=>t],["khz",t=>t/1e3]]),ql=new Map([["cm",t=>2.54*t],["mm",t=>25.4*t],["q",t=>25.4*t*4],["in",t=>t],["pc",t=>6*t],["pt",t=>72*t],["px",t=>96*t]]),Vl=new Map([["hz",t=>1e3*t],["khz",t=>t]]),Kl=new Map([["cm",t=>t/10],["mm",t=>t],["q",t=>4*t],["in",t=>t/25.4],["pc",t=>t/25.4*6],["pt",t=>t/25.4*72],["px",t=>t/25.4*96]]),Xl=new Map([["ms",t=>t],["s",t=>t/1e3]]),Zl=new Map([["cm",t=>t/6*2.54],["mm",t=>t/6*25.4],["q",t=>t/6*25.4*4],["in",t=>t/6],["pc",t=>t],["pt",t=>t/6*72],["px",t=>t/6*96]]),Yl=new Map([["cm",t=>t/72*2.54],["mm",t=>t/72*25.4],["q",t=>t/72*25.4*4],["in",t=>t/72],["pc",t=>t/72*6],["pt",t=>t],["px",t=>t/72*96]]),Ql=new Map([["cm",t=>t/96*2.54],["mm",t=>t/96*25.4],["q",t=>t/96*25.4*4],["in",t=>t/96],["pc",t=>t/96*6],["pt",t=>t/96*72],["px",t=>t]]),Jl=new Map([["cm",t=>t/4/10],["mm",t=>t/4],["q",t=>t],["in",t=>t/4/25.4],["pc",t=>t/4/25.4*6],["pt",t=>t/4/25.4*72],["px",t=>t/4/25.4*96]]),ho=new Map([["deg",t=>180*t/Math.PI],["grad",t=>180*t/Math.PI/.9],["rad",t=>t],["turn",t=>180*t/Math.PI/360]]),tc=new Map([["ms",t=>1e3*t],["s",t=>t]]),gr=new Map([["deg",t=>360*t],["grad",t=>360*t/.9],["rad",t=>360*t/180*Math.PI],["turn",t=>t]]),fo=new Map([["cm",Gl],["mm",Kl],["q",Jl],["in",ql],["pc",Zl],["pt",Yl],["px",Ql],["ms",Xl],["s",tc],["deg",rs],["grad",mr],["rad",ho],["turn",gr],["hz",jl],["khz",Vl]]);function Yt(t,e){if(!nt(t)||!nt(e))return e;const n=Ht(t[4].unit),r=Ht(e[4].unit);if(n===r)return e;const s=fo.get(r);if(!s)return e;const a=s.get(n);if(!a)return e;const o=a(e[4].value),i=[v.Dimension,"",e[2],e[3],{...e[4],signCharacter:o<0?"-":void 0,type:Number.isInteger(o)?x.Integer:x.Number,value:o}];return ro(i,t[4].unit),i}function ec(t){if(!nt(t))return t;const e=Ht(t[4].unit),n=zl[e];if(e===n)return t;const r=fo.get(e);if(!r)return t;const s=r.get(n);if(!s)return t;const a=s(t[4].value),o=[v.Dimension,"",t[2],t[3],{...t[4],signCharacter:a<0?"-":void 0,type:Number.isInteger(a)?x.Integer:x.Number,value:a}];return ro(o,n),o}function nc(t){if(t.length!==2)return-1;const e=t[0].value;let n=t[1].value;if(H(e)&&H(n)){const r=e[4].value+n[4].value;return new G([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number}])}if(tt(e)&&tt(n)){const r=e[4].value+n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(nt(e)&&nt(n)&&(n=Yt(e,n),Ht(e[4].unit)===Ht(n[4].unit))){const r=e[4].value+n[4].value;return new G([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number,unit:e[4].unit}])}return-1}function rc(t){if(t.length!==2)return-1;const e=t[0].value,n=t[1].value;if(H(e)&&H(n)){const r=e[4].value/n[4].value;return new G([v.Number,r.toString(),e[2],n[3],{value:r,type:Number.isInteger(r)?x.Integer:x.Number}])}if(tt(e)&&H(n)){const r=e[4].value/n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(nt(e)&&H(n)){const r=e[4].value/n[4].value;return new G([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:Number.isInteger(r)?x.Integer:x.Number,unit:e[4].unit}])}return-1}function yn(t){return!!t&&typeof t=="object"&&"inputs"in t&&Array.isArray(t.inputs)&&"operation"in t}function Qt(t){if(t===-1)return-1;const e=[];for(let n=0;n<t.inputs.length;n++){const r=t.inputs[n];if(I(r)){e.push(r);continue}const s=Qt(r);if(s===-1)return-1;e.push(s)}return t.operation(e)}function sc(t){if(t.length!==2)return-1;const e=t[0].value,n=t[1].value;if(H(e)&&H(n)){const r=e[4].value*n[4].value;return new G([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number}])}if(tt(e)&&H(n)){const r=e[4].value*n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(H(e)&&tt(n)){const r=e[4].value*n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(nt(e)&&H(n)){const r=e[4].value*n[4].value;return new G([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number,unit:e[4].unit}])}if(H(e)&&nt(n)){const r=e[4].value*n[4].value;return new G([v.Dimension,r.toString()+n[4].unit,e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number,unit:n[4].unit}])}return-1}function Ln(t,e){for(let n=0;n<t.length;n++){const r=t[n];if(!I(r))continue;const s=r.value;if(!vt(s))continue;const a=Ht(s[4].value);switch(a){case"e":t.splice(n,1,new G([v.Number,Math.E.toString(),s[2],s[3],{value:Math.E,type:x.Number}]));break;case"pi":t.splice(n,1,new G([v.Number,Math.PI.toString(),s[2],s[3],{value:Math.PI,type:x.Number}]));break;case"infinity":t.splice(n,1,new G([v.Number,"infinity",s[2],s[3],{value:1/0,type:x.Number}]));break;case"-infinity":t.splice(n,1,new G([v.Number,"-infinity",s[2],s[3],{value:-1/0,type:x.Number}]));break;case"nan":t.splice(n,1,new G([v.Number,"NaN",s[2],s[3],{value:Number.NaN,type:x.Number}]));break;default:if(e.has(a)){const o=e.get(a);t.splice(n,1,new G(o))}}}return t}function ss(t){if(t.length!==1)return-1;const e=t[0].value;return pt(e)?t[0]:-1}function Ke(t,e,n){return nt(e)?vr(t,e[4].unit,n):tt(e)?ac(t,n):H(e)?Ae(t,n):-1}function vr(t,e,n){const r=t.tokens();return{inputs:[new G([v.Dimension,n.toString()+e,r[0][2],r[r.length-1][3],{value:n,type:Number.isInteger(n)?x.Integer:x.Number,unit:e}])],operation:ss}}function ac(t,e){const n=t.tokens();return{inputs:[new G([v.Percentage,e.toString()+"%",n[0][2],n[n.length-1][3],{value:e}])],operation:ss}}function Ae(t,e){const n=t.tokens();return{inputs:[new G([v.Number,e.toString(),n[0][2],n[n.length-1][3],{value:e,type:Number.isInteger(e)?x.Integer:x.Number}])],operation:ss}}function oc(t,e){const n=e.value;return H(n)?vr(t,"rad",Math.acos(n[4].value)):-1}function ic(t,e){const n=e.value;return H(n)?vr(t,"rad",Math.asin(n[4].value)):-1}function lc(t,e){const n=e.value;return H(n)?vr(t,"rad",Math.atan(n[4].value)):-1}function as(t){return nt(t)||H(t)}function Ks(t){if(t.length===0)return!0;const e=t[0];if(!pt(e))return!1;if(t.length===1)return!0;if(nt(e)){const n=Ht(e[4].unit);for(let r=1;r<t.length;r++){const s=t[r];if(e[0]!==s[0]||n!==Ht(s[4].unit))return!1}return!0}for(let n=1;n<t.length;n++){const r=t[n];if(e[0]!==r[0])return!1}return!0}function Xe(t,e){return!!pt(t)&&(nt(t)?t[0]===e[0]&&Ht(t[4].unit)===Ht(e[4].unit):t[0]===e[0])}function cc(t,e,n){const r=e.value;if(!as(r))return-1;const s=Yt(r,n.value);return Xe(r,s)?vr(t,"rad",Math.atan2(r[4].value,s[4].value)):-1}function uc(t,e,n){const r=e.value;return!pt(r)||!n.rawPercentages&&tt(r)?-1:Ke(t,r,Math.abs(r[4].value))}function hc(t,e,n,r,s){if(!I(e)||!I(n)||!I(r))return-1;const a=e.value;if(!pt(a)||!s.rawPercentages&&tt(a))return-1;const o=Yt(a,n.value);if(!Xe(a,o))return-1;const i=Yt(a,r.value);return Xe(a,i)?Ke(t,a,Math.max(a[4].value,Math.min(o[4].value,i[4].value))):-1}function fc(t,e){const n=e.value;if(!as(n))return-1;let r=n[4].value;if(nt(n))switch(n[4].unit.toLowerCase()){case"rad":break;case"deg":r=rs.get("rad")(n[4].value);break;case"grad":r=mr.get("rad")(n[4].value);break;case"turn":r=gr.get("rad")(n[4].value);break;default:return-1}return r=Math.cos(r),Ae(t,r)}function pc(t,e){const n=e.value;return H(n)?Ae(t,Math.exp(n[4].value)):-1}function dc(t,e,n){if(!e.every(I))return-1;const r=e[0].value;if(!pt(r)||!n.rawPercentages&&tt(r))return-1;const s=e.map(i=>Yt(r,i.value));if(!Ks(s))return-1;const a=s.map(i=>i[4].value),o=Math.hypot(...a);return Ke(t,r,o)}function po(t,e,n){if(!e.every(I))return-1;const r=e[0].value;if(!pt(r)||!n.rawPercentages&&tt(r))return-1;const s=e.map(i=>Yt(r,i.value));if(!Ks(s))return-1;const a=s.map(i=>i[4].value),o=Math.max(...a);return Ke(t,r,o)}function mo(t,e,n){if(!e.every(I))return-1;const r=e[0].value;if(!pt(r)||!n.rawPercentages&&tt(r))return-1;const s=e.map(i=>Yt(r,i.value));if(!Ks(s))return-1;const a=s.map(i=>i[4].value),o=Math.min(...a);return Ke(t,r,o)}function mc(t,e,n){const r=e.value;if(!pt(r))return-1;const s=Yt(r,n.value);if(!Xe(r,s))return-1;let a;return a=s[4].value===0?Number.NaN:Number.isFinite(r[4].value)&&(Number.isFinite(s[4].value)||(s[4].value!==Number.POSITIVE_INFINITY||r[4].value!==Number.NEGATIVE_INFINITY&&!Object.is(0*r[4].value,-0))&&(s[4].value!==Number.NEGATIVE_INFINITY||r[4].value!==Number.POSITIVE_INFINITY&&!Object.is(0*r[4].value,0)))?Number.isFinite(s[4].value)?(r[4].value%s[4].value+s[4].value)%s[4].value:r[4].value:Number.NaN,Ke(t,r,a)}function gc(t,e,n){const r=e.value,s=n.value;return!H(r)||!Xe(r,s)?-1:Ae(t,Math.pow(r[4].value,s[4].value))}function vc(t,e,n){const r=e.value;if(!pt(r))return-1;const s=Yt(r,n.value);if(!Xe(r,s))return-1;let a;return a=s[4].value===0?Number.NaN:Number.isFinite(r[4].value)?Number.isFinite(s[4].value)?r[4].value%s[4].value:r[4].value:Number.NaN,Ke(t,r,a)}function bc(t,e,n,r,s){const a=n.value;if(!pt(a)||!s.rawPercentages&&tt(a))return-1;const o=Yt(a,r.value);if(!Xe(a,o))return-1;let i;if(o[4].value===0)i=Number.NaN;else if(Number.isFinite(a[4].value)||Number.isFinite(o[4].value))if(!Number.isFinite(a[4].value)&&Number.isFinite(o[4].value))i=a[4].value;else if(Number.isFinite(a[4].value)&&!Number.isFinite(o[4].value))switch(e){case"down":i=a[4].value<0?-1/0:Object.is(-0,0*a[4].value)?-0:0;break;case"up":i=a[4].value>0?1/0:Object.is(0,0*a[4].value)?0:-0;break;default:i=Object.is(0,0*a[4].value)?0:-0}else if(Number.isFinite(o[4].value))switch(e){case"down":i=Math.floor(a[4].value/o[4].value)*o[4].value;break;case"up":i=Math.ceil(a[4].value/o[4].value)*o[4].value;break;case"to-zero":i=Math.trunc(a[4].value/o[4].value)*o[4].value;break;default:{let l=Math.floor(a[4].value/o[4].value)*o[4].value,c=Math.ceil(a[4].value/o[4].value)*o[4].value;if(l>c){const d=l;l=c,c=d}const h=Math.abs(a[4].value-l),f=Math.abs(a[4].value-c);i=h===f?c:h<f?l:c;break}}else i=a[4].value;else i=Number.NaN;return Ke(t,a,i)}function wc(t,e,n){const r=e.value;return!pt(r)||!n.rawPercentages&&tt(r)?-1:Ae(t,Math.sign(r[4].value))}function $c(t,e){const n=e.value;if(!as(n))return-1;let r=n[4].value;if(nt(n))switch(Ht(n[4].unit)){case"rad":break;case"deg":r=rs.get("rad")(n[4].value);break;case"grad":r=mr.get("rad")(n[4].value);break;case"turn":r=gr.get("rad")(n[4].value);break;default:return-1}return r=Math.sin(r),Ae(t,r)}function yc(t,e){const n=e.value;return H(n)?Ae(t,Math.sqrt(n[4].value)):-1}function Nc(t,e){const n=e.value;if(!as(n))return-1;const r=n[4].value;let s=0,a=n[4].value;if(nt(n))switch(Ht(n[4].unit)){case"rad":s=ho.get("deg")(r);break;case"deg":s=r,a=rs.get("rad")(r);break;case"grad":s=mr.get("deg")(r),a=mr.get("rad")(r);break;case"turn":s=gr.get("deg")(r),a=gr.get("rad")(r);break;default:return-1}const o=s/90;return a=s%90==0&&o%2!=0?o>0?1/0:-1/0:Math.tan(a),Ae(t,a)}function Ec(t){if(t.length!==2)return-1;const e=t[0].value;let n=t[1].value;if(H(e)&&H(n)){const r=e[4].value-n[4].value;return new G([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number}])}if(tt(e)&&tt(n)){const r=e[4].value-n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(nt(e)&&nt(n)&&(n=Yt(e,n),Ht(e[4].unit)===Ht(n[4].unit))){const r=e[4].value-n[4].value;return new G([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===x.Integer&&n[4].type===x.Integer?x.Integer:x.Number,unit:e[4].unit}])}return-1}function Cc(t,e){if(e.length===1){const n=e[0];if(!n||!I(n))return-1;const r=n.value;return H(r)?Ae(t,Math.log(r[4].value)):-1}if(e.length===2){const n=e[0];if(!n||!I(n))return-1;const r=n.value;if(!H(r))return-1;const s=e[1];if(!s||!I(s))return-1;const a=s.value;return H(a)?Ae(t,Math.log(r[4].value)/Math.log(a[4].value)):-1}return-1}const kc=/^none$/i;function Xs(t){if(Array.isArray(t)){const n=t.filter(r=>!(ae(r)&&oe(r)));return n.length===1&&Xs(n[0])}if(!I(t))return!1;const e=t.value;return!!vt(e)&&kc.test(e[4].value)}function Fc(t,e,n,r,s,a){const o=n.value;if(!pt(o))return-1;const i=Yt(o,r.value);if(!Xe(o,i))return-1;let l,c=null;if(s&&(c=Yt(o,s.value),!Xe(o,c)))return-1;if(Number.isFinite(o[4].value))if(Number.isFinite(i[4].value))if(Number.isFinite(i[4].value-o[4].value))if(c&&!Number.isFinite(c[4].value))l=o[4].value;else{const h=xc(Sc([e,be(o),be(i),s?`by ${s.toString()}`:""].join(",")),a.randomSeed);let f=o[4].value,d=i[4].value;if(f>d&&([f,d]=[d,f]),c&&(c[4].value<=0||Math.abs(f-d)/c[4].value>1e10)&&(c=null),c){const p=Math.abs(f-d),m=h();l=f+Math.floor(p/c[4].value*m)*c[4].value}else{const p=h();l=Number((p*(d-f)+f).toFixed(5))}}else l=Number.NaN;else l=Number.NaN;else l=Number.NaN;return Ke(t,o,l)}function xc(t=.34944106645296036,e=.19228640875738723,n=.8784393832007205,r=.04850964319275053){return()=>{const s=((t|=0)+(e|=0)|0)+(r|=0)|0;return r=r+1|0,t=e^e>>>9,e=(n|=0)+(n<<3)|0,n=(n=n<<21|n>>>11)+s|0,(s>>>0)/4294967296}}function Sc(t){let e=0,n=0,r=0;e=~e;for(let s=0,a=t.length;s<a;s++)r=255&(e^t.charCodeAt(s)),n=+("0x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substring(9*r,9*r+8)),e=e>>>8^n;return~e>>>0}const Zs=new Map([["abs",function(t,e,n){return Pe(t,e,n,uc)}],["acos",function(t,e,n){return Pe(t,e,n,oc)}],["asin",function(t,e,n){return Pe(t,e,n,ic)}],["atan",function(t,e,n){return Pe(t,e,n,lc)}],["atan2",function(t,e,n){return os(t,e,n,cc)}],["calc",Ut],["clamp",function(t,e,n){const r=Ln([...t.value.filter(m=>!$n(m))],e),s=[],a=[],o=[];{let m=s;for(let y=0;y<r.length;y++){const b=r[y];if(I(b)&&ne(b.value)){if(m===o)return-1;if(m===a){m=o;continue}if(m===s){m=a;continue}return-1}m.push(b)}}const i=Xs(s),l=Xs(o);if(i&&l)return Ut(ie(a),e,n);const c=Qt(Ut(ie(a),e,n));if(c===-1)return-1;if(i){const m=Qt(Ut(ie(o),e,n));return m===-1?-1:mo((h=c,f=m,new _t([v.Function,"min(",-1,-1,{value:"min"}],[v.CloseParen,")",-1,-1,void 0],[h,new G([v.Comma,",",-1,-1,void 0]),f])),[c,m],n)}if(l){const m=Qt(Ut(ie(s),e,n));return m===-1?-1:po(Mc(m,c),[m,c],n)}var h,f;const d=Qt(Ut(ie(s),e,n));if(d===-1)return-1;const p=Qt(Ut(ie(o),e,n));return p===-1?-1:hc(t,d,c,p,n)}],["cos",function(t,e,n){return Pe(t,e,n,fc)}],["exp",function(t,e,n){return Pe(t,e,n,pc)}],["hypot",function(t,e,n){return is(t,t.value,e,n,dc)}],["log",function(t,e,n){return is(t,t.value,e,n,Cc)}],["max",function(t,e,n){return is(t,t.value,e,n,po)}],["min",function(t,e,n){return is(t,t.value,e,n,mo)}],["mod",function(t,e,n){return os(t,e,n,mc)}],["pow",function(t,e,n){return os(t,e,n,gc)}],["random",function(t,e,n){const r=t.value.filter(f=>!$n(f));let s="";const a=[],o=[];for(let f=0;f<r.length;f++){const d=r[f];if(!s&&o.length===0&&I(d)&&vt(d.value)){const p=d.value[4].value.toLowerCase();if(p==="per-element"||p.startsWith("--")){s=p;const m=r[f+1];if(!I(m)||!ne(m.value))return-1;f++;continue}}if(I(d)&&ne(d.value)){const p=r[f+1];if(o.length>0&&I(p)&&vt(p.value)){const m=p.value[4].value.toLowerCase();if(m==="by"||m.startsWith("--")){a.push(...r.slice(f+2));break}}}o.push(d)}const i=vo(o,e,n);if(i===-1)return-1;const[l,c]=i;let h=null;return a.length&&(h=go(a,e,n),h===-1)?-1:Fc(t,s,l,c,h,n)}],["rem",function(t,e,n){return os(t,e,n,vc)}],["round",function(t,e,n){const r=Ln([...t.value.filter(h=>!$n(h))],e);let s="",a=!1;const o=[],i=[];{let h=o;for(let f=0;f<r.length;f++){const d=r[f];if(!s&&o.length===0&&i.length===0&&I(d)&&vt(d.value)){const p=d.value[4].value.toLowerCase();if(Pc.has(p)){s=p;continue}}if(I(d)&&ne(d.value)){if(h===i)return-1;if(h===o&&s&&o.length===0)continue;if(h===o){a=!0,h=i;continue}return-1}h.push(d)}}const l=Qt(Ut(ie(o),e,n));if(l===-1)return-1;a||i.length!==0||i.push(new G([v.Number,"1",-1,-1,{value:1,type:x.Integer}]));const c=Qt(Ut(ie(i),e,n));return c===-1?-1:(s||(s="nearest"),bc(t,s,l,c,n))}],["sign",function(t,e,n){return Pe(t,e,n,wc)}],["sin",function(t,e,n){return Pe(t,e,n,$c)}],["sqrt",function(t,e,n){return Pe(t,e,n,yc)}],["tan",function(t,e,n){return Pe(t,e,n,Nc)}]]);function Ut(t,e,n){const r=Ln([...t.value.filter(a=>!$n(a))],e);if(r.length===1&&I(r[0]))return{inputs:[r[0]],operation:ss};let s=0;for(;s<r.length;){const a=r[s];if(Hl(a)&&oo(a.startToken)){const o=Ut(a,e,n);if(o===-1)return-1;r.splice(s,1,o)}else if(se(a)){const o=Zs.get(a.getName().toLowerCase());if(!o)return-1;const i=o(a,e,n);if(i===-1)return-1;r.splice(s,1,i)}else s++}if(s=0,r.length===1&&yn(r[0]))return r[0];for(;s<r.length;){const a=r[s];if(!a||!I(a)&&!yn(a)){s++;continue}const o=r[s+1];if(!o||!I(o)){s++;continue}const i=o.value;if(!es(i)||i[4].value!=="*"&&i[4].value!=="/"){s++;continue}const l=r[s+2];if(!l||!I(l)&&!yn(l))return-1;i[4].value!=="*"?i[4].value!=="/"?s++:r.splice(s,3,{inputs:[a,l],operation:rc}):r.splice(s,3,{inputs:[a,l],operation:sc})}if(s=0,r.length===1&&yn(r[0]))return r[0];for(;s<r.length;){const a=r[s];if(!a||!I(a)&&!yn(a)){s++;continue}const o=r[s+1];if(!o||!I(o)){s++;continue}const i=o.value;if(!es(i)||i[4].value!=="+"&&i[4].value!=="-"){s++;continue}const l=r[s+2];if(!l||!I(l)&&!yn(l))return-1;i[4].value!=="+"?i[4].value!=="-"?s++:r.splice(s,3,{inputs:[a,l],operation:Ec}):r.splice(s,3,{inputs:[a,l],operation:nc})}return r.length===1&&yn(r[0])?r[0]:-1}function Pe(t,e,n,r){const s=go(t.value,e,n);return s===-1?-1:r(t,s,n)}function go(t,e,n){const r=Qt(Ut(ie(Ln([...t.filter(s=>!$n(s))],e)),e,n));return r===-1?-1:r}function os(t,e,n,r){const s=vo(t.value,e,n);if(s===-1)return-1;const[a,o]=s;return r(t,a,o,n)}function vo(t,e,n){const r=Ln([...t.filter(l=>!$n(l))],e),s=[],a=[];{let l=s;for(let c=0;c<r.length;c++){const h=r[c];if(I(h)&&ne(h.value)){if(l===a)return-1;if(l===s){l=a;continue}return-1}l.push(h)}}const o=Qt(Ut(ie(s),e,n));if(o===-1)return-1;const i=Qt(Ut(ie(a),e,n));return i===-1?-1:[o,i]}function is(t,e,n,r,s){const a=Ac(t.value,n,r);return a===-1?-1:s(t,a,r)}function Ac(t,e,n){const r=Ln([...t.filter(a=>!$n(a))],e),s=[];{const a=[];let o=[];for(let i=0;i<r.length;i++){const l=r[i];I(l)&&ne(l.value)?(a.push(o),o=[]):o.push(l)}a.push(o);for(let i=0;i<a.length;i++){if(a[i].length===0)return-1;const l=Qt(Ut(ie(a[i]),e,n));if(l===-1)return-1;s.push(l)}}return s}const Pc=new Set(["nearest","up","down","to-zero"]);function ie(t){return new _t([v.Function,"calc(",-1,-1,{value:"calc"}],[v.CloseParen,")",-1,-1,void 0],t)}function Mc(t,e){return new _t([v.Function,"max(",-1,-1,{value:"max"}],[v.CloseParen,")",-1,-1,void 0],[t,new G([v.Comma,",",-1,-1,void 0]),e])}function Dc(t){if(t===-1)return-1;if(se(t))return t;const e=t.value;return pt(e)&&Number.isNaN(e[4].value)?H(e)?new _t([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,"NaN",e[2],e[3],{value:"NaN"}])]):nt(e)?new _t([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,"NaN",e[2],e[3],{value:"NaN"}]),new te([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Delim,"*",e[2],e[3],{value:"*"}]),new te([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Dimension,"1"+e[4].unit,e[2],e[3],{value:1,type:x.Integer,unit:e[4].unit}])]):tt(e)?new _t([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,"NaN",e[2],e[3],{value:"NaN"}]),new te([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Delim,"*",e[2],e[3],{value:"*"}]),new te([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Percentage,"1%",e[2],e[3],{value:1}])]):-1:t}function Bc(t){if(t===-1)return-1;if(se(t))return t;const e=t.value;if(!pt(e)||Number.isFinite(e[4].value)||Number.isNaN(e[4].value))return t;let n="";return Number.NEGATIVE_INFINITY===e[4].value&&(n="-"),H(e)?new _t([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}])]):nt(e)?new _t([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}]),new te([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Delim,"*",e[2],e[3],{value:"*"}]),new te([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Dimension,"1"+e[4].unit,e[2],e[3],{value:1,type:x.Integer,unit:e[4].unit}])]):new _t([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}]),new te([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Delim,"*",e[2],e[3],{value:"*"}]),new te([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Percentage,"1%",e[2],e[3],{value:1}])])}function Rc(t){if(t===-1)return-1;if(se(t))return t;const e=t.value;return pt(e)&&Object.is(-0,e[4].value)&&(e[1]==="-0"||(tt(e)?e[1]="-0%":nt(e)?e[1]="-0"+e[4].unit:e[1]="-0")),t}function Oc(t,e=13){if(t===-1)return-1;if(e<=0||se(t))return t;const n=t.value;if(!pt(n)||Number.isInteger(n[4].value))return t;const r=Number(n[4].value.toFixed(e)).toString();return H(n)?n[1]=r:tt(n)?n[1]=r+"%":nt(n)&&(n[1]=r+n[4].unit),t}function Wc(t){return t===-1?-1:(se(t)||nt(t.value)&&(t.value=ec(t.value)),t)}function Tc(t,e){let n=t;return e!=null&&e.toCanonicalUnits&&(n=Wc(n)),n=Oc(n,e?.precision),n=Rc(n),e!=null&&e.censorIntoStandardRepresentableValues||(n=Dc(n),n=Bc(n)),n}function Lc(t){const e=new Map;if(!t)return e;for(const[n,r]of t)if(qs(r))e.set(n,r);else if(typeof r=="string"){const s=no({css:r}),a=s.nextToken();if(s.nextToken(),!s.endOfFile()||!pt(a))continue;e.set(n,a)}return e}function Wt(t,e){return br(Tl(Ve({css:t}),{}),e).map(n=>n.map(r=>be(...r.tokens())).join("")).join(",")}function br(t,e){const n=Lc(e?.globals);return _l(t,r=>{if(!se(r))return;const s=Zs.get(r.getName().toLowerCase());if(!s)return;const a=Tc(Qt(s(r,n,e??{})),e);return a!==-1?a:void 0})}const ls=new Set(Zs.keys()),In=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,bo=new Set,Ys=typeof process=="object"&&process?process:{},wo=(t,e,n,r)=>{typeof Ys.emitWarning=="function"?Ys.emitWarning(t,e,n,r):console.error(`[${n}] ${e}: ${t}`)};let cs=globalThis.AbortController,$o=globalThis.AbortSignal;var yo;if(typeof cs>"u"){$o=class{constructor(){K(this,"onabort"),K(this,"_onabort",[]),K(this,"reason"),K(this,"aborted",!1)}addEventListener(n,r){this._onabort.push(r)}},cs=class{constructor(){K(this,"signal",new $o),e()}abort(n){var r,s;if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(const a of this.signal._onabort)a(n);(s=(r=this.signal).onabort)==null||s.call(r,n)}}};let t=((yo=Ys.env)==null?void 0:yo.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const e=()=>{t&&(t=!1,wo("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}const Ic=t=>!bo.has(t),on=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),No=t=>on(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?us:null:null;class us extends Array{constructor(e){super(e),this.fill(0)}}var wr;const Eo=class Kr{constructor(e,n){if(K(this,"heap"),K(this,"length"),!u(Kr,wr))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(e),this.length=0}static create(e){const n=No(e);if(!n)return[];C(Kr,wr,!0);const r=new Kr(e,n);return C(Kr,wr,!1),r}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}};wr=new WeakMap,z(Eo,wr,!1);let _c=Eo;var Co,ko,Me,le,De,Be,$r,yr,Ct,ce,yt,lt,X,zt,ue,Tt,kt,Re,Ft,Oe,We,he,Te,Nn,Gt,W,Qs,_n,ln,hs,fe,Fo,Hn,Nr,fs,cn,un,Js,ps,ds,at,ta,Er,hn,ea;const Hc=class rl{constructor(e){z(this,W),z(this,Me),z(this,le),z(this,De),z(this,Be),z(this,$r),z(this,yr),K(this,"ttl"),K(this,"ttlResolution"),K(this,"ttlAutopurge"),K(this,"updateAgeOnGet"),K(this,"updateAgeOnHas"),K(this,"allowStale"),K(this,"noDisposeOnSet"),K(this,"noUpdateTTL"),K(this,"maxEntrySize"),K(this,"sizeCalculation"),K(this,"noDeleteOnFetchRejection"),K(this,"noDeleteOnStaleGet"),K(this,"allowStaleOnFetchAbort"),K(this,"allowStaleOnFetchRejection"),K(this,"ignoreFetchAbort"),z(this,Ct),z(this,ce),z(this,yt),z(this,lt),z(this,X),z(this,zt),z(this,ue),z(this,Tt),z(this,kt),z(this,Re),z(this,Ft),z(this,Oe),z(this,We),z(this,he),z(this,Te),z(this,Nn),z(this,Gt),z(this,_n,()=>{}),z(this,ln,()=>{}),z(this,hs,()=>{}),z(this,fe,()=>!1),z(this,Hn,R=>{}),z(this,Nr,(R,U,j)=>{}),z(this,fs,(R,U,j,Z)=>{if(j||Z)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0}),K(this,Co,"LRUCache");const{max:n=0,ttl:r,ttlResolution:s=1,ttlAutopurge:a,updateAgeOnGet:o,updateAgeOnHas:i,allowStale:l,dispose:c,disposeAfter:h,noDisposeOnSet:f,noUpdateTTL:d,maxSize:p=0,maxEntrySize:m=0,sizeCalculation:y,fetchMethod:b,memoMethod:w,noDeleteOnFetchRejection:N,noDeleteOnStaleGet:E,allowStaleOnFetchRejection:M,allowStaleOnFetchAbort:k,ignoreFetchAbort:O}=e;if(n!==0&&!on(n))throw new TypeError("max option must be a nonnegative integer");const A=n?No(n):Array;if(!A)throw new Error("invalid max value: "+n);if(C(this,Me,n),C(this,le,p),this.maxEntrySize=m||u(this,le),this.sizeCalculation=y,this.sizeCalculation){if(!u(this,le)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(w!==void 0&&typeof w!="function")throw new TypeError("memoMethod must be a function if defined");if(C(this,yr,w),b!==void 0&&typeof b!="function")throw new TypeError("fetchMethod must be a function if specified");if(C(this,$r,b),C(this,Nn,!!b),C(this,yt,new Map),C(this,lt,new Array(n).fill(void 0)),C(this,X,new Array(n).fill(void 0)),C(this,zt,new A(n)),C(this,ue,new A(n)),C(this,Tt,0),C(this,kt,0),C(this,Re,_c.create(n)),C(this,Ct,0),C(this,ce,0),typeof c=="function"&&C(this,De,c),typeof h=="function"?(C(this,Be,h),C(this,Ft,[])):(C(this,Be,void 0),C(this,Ft,void 0)),C(this,Te,!!u(this,De)),C(this,Gt,!!u(this,Be)),this.noDisposeOnSet=!!f,this.noUpdateTTL=!!d,this.noDeleteOnFetchRejection=!!N,this.allowStaleOnFetchRejection=!!M,this.allowStaleOnFetchAbort=!!k,this.ignoreFetchAbort=!!O,this.maxEntrySize!==0){if(u(this,le)!==0&&!on(u(this,le)))throw new TypeError("maxSize must be a positive integer if specified");if(!on(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");L(this,W,Fo).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!E,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!i,this.ttlResolution=on(s)||s===0?s:1,this.ttlAutopurge=!!a,this.ttl=r||0,this.ttl){if(!on(this.ttl))throw new TypeError("ttl must be a positive integer if specified");L(this,W,Qs).call(this)}if(u(this,Me)===0&&this.ttl===0&&u(this,le)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!u(this,Me)&&!u(this,le)){const R="LRU_CACHE_UNBOUNDED";Ic(R)&&(bo.add(R),wo("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",R,rl))}}static unsafeExposeInternals(e){return{starts:u(e,We),ttls:u(e,he),sizes:u(e,Oe),keyMap:u(e,yt),keyList:u(e,lt),valList:u(e,X),next:u(e,zt),prev:u(e,ue),get head(){return u(e,Tt)},get tail(){return u(e,kt)},free:u(e,Re),isBackgroundFetch:n=>{var r;return L(r=e,W,at).call(r,n)},backgroundFetch:(n,r,s,a)=>{var o;return L(o=e,W,ds).call(o,n,r,s,a)},moveToTail:n=>{var r;return L(r=e,W,Er).call(r,n)},indexes:n=>{var r;return L(r=e,W,cn).call(r,n)},rindexes:n=>{var r;return L(r=e,W,un).call(r,n)},isStale:n=>{var r;return u(r=e,fe).call(r,n)}}}get max(){return u(this,Me)}get maxSize(){return u(this,le)}get calculatedSize(){return u(this,ce)}get size(){return u(this,Ct)}get fetchMethod(){return u(this,$r)}get memoMethod(){return u(this,yr)}get dispose(){return u(this,De)}get disposeAfter(){return u(this,Be)}getRemainingTTL(e){return u(this,yt).has(e)?1/0:0}*entries(){for(const e of L(this,W,cn).call(this))u(this,X)[e]!==void 0&&u(this,lt)[e]!==void 0&&!L(this,W,at).call(this,u(this,X)[e])&&(yield[u(this,lt)[e],u(this,X)[e]])}*rentries(){for(const e of L(this,W,un).call(this))u(this,X)[e]!==void 0&&u(this,lt)[e]!==void 0&&!L(this,W,at).call(this,u(this,X)[e])&&(yield[u(this,lt)[e],u(this,X)[e]])}*keys(){for(const e of L(this,W,cn).call(this)){const n=u(this,lt)[e];n!==void 0&&!L(this,W,at).call(this,u(this,X)[e])&&(yield n)}}*rkeys(){for(const e of L(this,W,un).call(this)){const n=u(this,lt)[e];n!==void 0&&!L(this,W,at).call(this,u(this,X)[e])&&(yield n)}}*values(){for(const e of L(this,W,cn).call(this))u(this,X)[e]!==void 0&&!L(this,W,at).call(this,u(this,X)[e])&&(yield u(this,X)[e])}*rvalues(){for(const e of L(this,W,un).call(this))u(this,X)[e]!==void 0&&!L(this,W,at).call(this,u(this,X)[e])&&(yield u(this,X)[e])}[(ko=Symbol.iterator,Co=Symbol.toStringTag,ko)](){return this.entries()}find(e,n={}){for(const r of L(this,W,cn).call(this)){const s=u(this,X)[r],a=L(this,W,at).call(this,s)?s.__staleWhileFetching:s;if(a!==void 0&&e(a,u(this,lt)[r],this))return this.get(u(this,lt)[r],n)}}forEach(e,n=this){for(const r of L(this,W,cn).call(this)){const s=u(this,X)[r],a=L(this,W,at).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&e.call(n,a,u(this,lt)[r],this)}}rforEach(e,n=this){for(const r of L(this,W,un).call(this)){const s=u(this,X)[r],a=L(this,W,at).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&e.call(n,a,u(this,lt)[r],this)}}purgeStale(){let e=!1;for(const n of L(this,W,un).call(this,{allowStale:!0}))u(this,fe).call(this,n)&&(L(this,W,hn).call(this,u(this,lt)[n],"expire"),e=!0);return e}info(e){const n=u(this,yt).get(e);if(n===void 0)return;const r=u(this,X)[n],s=L(this,W,at).call(this,r)?r.__staleWhileFetching:r;if(s===void 0)return;const a={value:s};if(u(this,he)&&u(this,We)){const o=u(this,he)[n],i=u(this,We)[n];if(o&&i){const l=o-(In.now()-i);a.ttl=l,a.start=Date.now()}}return u(this,Oe)&&(a.size=u(this,Oe)[n]),a}dump(){const e=[];for(const n of L(this,W,cn).call(this,{allowStale:!0})){const r=u(this,lt)[n],s=u(this,X)[n],a=L(this,W,at).call(this,s)?s.__staleWhileFetching:s;if(a===void 0||r===void 0)continue;const o={value:a};if(u(this,he)&&u(this,We)){o.ttl=u(this,he)[n];const i=In.now()-u(this,We)[n];o.start=Math.floor(Date.now()-i)}u(this,Oe)&&(o.size=u(this,Oe)[n]),e.unshift([r,o])}return e}load(e){this.clear();for(const[n,r]of e){if(r.start){const s=Date.now()-r.start;r.start=In.now()-s}this.set(n,r.value,r)}}set(e,n,r={}){var s,a,o,i,l;if(n===void 0)return this.delete(e),this;const{ttl:c=this.ttl,start:h,noDisposeOnSet:f=this.noDisposeOnSet,sizeCalculation:d=this.sizeCalculation,status:p}=r;let{noUpdateTTL:m=this.noUpdateTTL}=r;const y=u(this,fs).call(this,e,n,r.size||0,d);if(this.maxEntrySize&&y>this.maxEntrySize)return p&&(p.set="miss",p.maxEntrySizeExceeded=!0),L(this,W,hn).call(this,e,"set"),this;let b=u(this,Ct)===0?void 0:u(this,yt).get(e);if(b===void 0)b=u(this,Ct)===0?u(this,kt):u(this,Re).length!==0?u(this,Re).pop():u(this,Ct)===u(this,Me)?L(this,W,ps).call(this,!1):u(this,Ct),u(this,lt)[b]=e,u(this,X)[b]=n,u(this,yt).set(e,b),u(this,zt)[u(this,kt)]=b,u(this,ue)[b]=u(this,kt),C(this,kt,b),Hs(this,Ct)._++,u(this,Nr).call(this,b,y,p),p&&(p.set="add"),m=!1;else{L(this,W,Er).call(this,b);const w=u(this,X)[b];if(n!==w){if(u(this,Nn)&&L(this,W,at).call(this,w)){w.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:N}=w;N!==void 0&&!f&&(u(this,Te)&&((s=u(this,De))==null||s.call(this,N,e,"set")),u(this,Gt)&&((a=u(this,Ft))==null||a.push([N,e,"set"])))}else f||(u(this,Te)&&((o=u(this,De))==null||o.call(this,w,e,"set")),u(this,Gt)&&((i=u(this,Ft))==null||i.push([w,e,"set"])));if(u(this,Hn).call(this,b),u(this,Nr).call(this,b,y,p),u(this,X)[b]=n,p){p.set="replace";const N=w&&L(this,W,at).call(this,w)?w.__staleWhileFetching:w;N!==void 0&&(p.oldValue=N)}}else p&&(p.set="update")}if(c!==0&&!u(this,he)&&L(this,W,Qs).call(this),u(this,he)&&(m||u(this,hs).call(this,b,c,h),p&&u(this,ln).call(this,p,b)),!f&&u(this,Gt)&&u(this,Ft)){const w=u(this,Ft);let N;for(;N=w?.shift();)(l=u(this,Be))==null||l.call(this,...N)}return this}pop(){var e;try{for(;u(this,Ct);){const n=u(this,X)[u(this,Tt)];if(L(this,W,ps).call(this,!0),L(this,W,at).call(this,n)){if(n.__staleWhileFetching)return n.__staleWhileFetching}else if(n!==void 0)return n}}finally{if(u(this,Gt)&&u(this,Ft)){const n=u(this,Ft);let r;for(;r=n?.shift();)(e=u(this,Be))==null||e.call(this,...r)}}}has(e,n={}){const{updateAgeOnHas:r=this.updateAgeOnHas,status:s}=n,a=u(this,yt).get(e);if(a!==void 0){const o=u(this,X)[a];if(L(this,W,at).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(u(this,fe).call(this,a))s&&(s.has="stale",u(this,ln).call(this,s,a));else return r&&u(this,_n).call(this,a),s&&(s.has="hit",u(this,ln).call(this,s,a)),!0}else s&&(s.has="miss");return!1}peek(e,n={}){const{allowStale:r=this.allowStale}=n,s=u(this,yt).get(e);if(s===void 0||!r&&u(this,fe).call(this,s))return;const a=u(this,X)[s];return L(this,W,at).call(this,a)?a.__staleWhileFetching:a}async fetch(e,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:i=this.noDisposeOnSet,size:l=0,sizeCalculation:c=this.sizeCalculation,noUpdateTTL:h=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:d=this.allowStaleOnFetchRejection,ignoreFetchAbort:p=this.ignoreFetchAbort,allowStaleOnFetchAbort:m=this.allowStaleOnFetchAbort,context:y,forceRefresh:b=!1,status:w,signal:N}=n;if(!u(this,Nn))return w&&(w.fetch="get"),this.get(e,{allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,status:w});const E={allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,ttl:o,noDisposeOnSet:i,size:l,sizeCalculation:c,noUpdateTTL:h,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:d,allowStaleOnFetchAbort:m,ignoreFetchAbort:p,status:w,signal:N};let M=u(this,yt).get(e);if(M===void 0){w&&(w.fetch="miss");const k=L(this,W,ds).call(this,e,M,E,y);return k.__returned=k}else{const k=u(this,X)[M];if(L(this,W,at).call(this,k)){const U=r&&k.__staleWhileFetching!==void 0;return w&&(w.fetch="inflight",U&&(w.returnedStale=!0)),U?k.__staleWhileFetching:k.__returned=k}const O=u(this,fe).call(this,M);if(!b&&!O)return w&&(w.fetch="hit"),L(this,W,Er).call(this,M),s&&u(this,_n).call(this,M),w&&u(this,ln).call(this,w,M),k;const A=L(this,W,ds).call(this,e,M,E,y),R=A.__staleWhileFetching!==void 0&&r;return w&&(w.fetch=O?"stale":"refresh",R&&O&&(w.returnedStale=!0)),R?A.__staleWhileFetching:A.__returned=A}}async forceFetch(e,n={}){const r=await this.fetch(e,n);if(r===void 0)throw new Error("fetch() returned undefined");return r}memo(e,n={}){const r=u(this,yr);if(!r)throw new Error("no memoMethod provided to constructor");const{context:s,forceRefresh:a,...o}=n,i=this.get(e,o);if(!a&&i!==void 0)return i;const l=r(e,i,{options:o,context:s});return this.set(e,l,o),l}get(e,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,status:o}=n,i=u(this,yt).get(e);if(i!==void 0){const l=u(this,X)[i],c=L(this,W,at).call(this,l);return o&&u(this,ln).call(this,o,i),u(this,fe).call(this,i)?(o&&(o.get="stale"),c?(o&&r&&l.__staleWhileFetching!==void 0&&(o.returnedStale=!0),r?l.__staleWhileFetching:void 0):(a||L(this,W,hn).call(this,e,"expire"),o&&r&&(o.returnedStale=!0),r?l:void 0)):(o&&(o.get="hit"),c?l.__staleWhileFetching:(L(this,W,Er).call(this,i),s&&u(this,_n).call(this,i),l))}else o&&(o.get="miss")}delete(e){return L(this,W,hn).call(this,e,"delete")}clear(){return L(this,W,ea).call(this,"delete")}};Me=new WeakMap,le=new WeakMap,De=new WeakMap,Be=new WeakMap,$r=new WeakMap,yr=new WeakMap,Ct=new WeakMap,ce=new WeakMap,yt=new WeakMap,lt=new WeakMap,X=new WeakMap,zt=new WeakMap,ue=new WeakMap,Tt=new WeakMap,kt=new WeakMap,Re=new WeakMap,Ft=new WeakMap,Oe=new WeakMap,We=new WeakMap,he=new WeakMap,Te=new WeakMap,Nn=new WeakMap,Gt=new WeakMap,W=new WeakSet,Qs=function(){const t=new us(u(this,Me)),e=new us(u(this,Me));C(this,he,t),C(this,We,e),C(this,hs,(s,a,o=In.now())=>{if(e[s]=a!==0?o:0,t[s]=a,a!==0&&this.ttlAutopurge){const i=setTimeout(()=>{u(this,fe).call(this,s)&&L(this,W,hn).call(this,u(this,lt)[s],"expire")},a+1);i.unref&&i.unref()}}),C(this,_n,s=>{e[s]=t[s]!==0?In.now():0}),C(this,ln,(s,a)=>{if(t[a]){const o=t[a],i=e[a];if(!o||!i)return;s.ttl=o,s.start=i,s.now=n||r();const l=s.now-i;s.remainingTTL=o-l}});let n=0;const r=()=>{const s=In.now();if(this.ttlResolution>0){n=s;const a=setTimeout(()=>n=0,this.ttlResolution);a.unref&&a.unref()}return s};this.getRemainingTTL=s=>{const a=u(this,yt).get(s);if(a===void 0)return 0;const o=t[a],i=e[a];if(!o||!i)return 1/0;const l=(n||r())-i;return o-l},C(this,fe,s=>{const a=e[s],o=t[s];return!!o&&!!a&&(n||r())-a>o})},_n=new WeakMap,ln=new WeakMap,hs=new WeakMap,fe=new WeakMap,Fo=function(){const t=new us(u(this,Me));C(this,ce,0),C(this,Oe,t),C(this,Hn,e=>{C(this,ce,u(this,ce)-t[e]),t[e]=0}),C(this,fs,(e,n,r,s)=>{if(L(this,W,at).call(this,n))return 0;if(!on(r))if(s){if(typeof s!="function")throw new TypeError("sizeCalculation must be a function");if(r=s(n,e),!on(r))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return r}),C(this,Nr,(e,n,r)=>{if(t[e]=n,u(this,le)){const s=u(this,le)-t[e];for(;u(this,ce)>s;)L(this,W,ps).call(this,!0)}C(this,ce,u(this,ce)+t[e]),r&&(r.entrySize=n,r.totalCalculatedSize=u(this,ce))})},Hn=new WeakMap,Nr=new WeakMap,fs=new WeakMap,cn=function*({allowStale:t=this.allowStale}={}){if(u(this,Ct))for(let e=u(this,kt);!(!L(this,W,Js).call(this,e)||((t||!u(this,fe).call(this,e))&&(yield e),e===u(this,Tt)));)e=u(this,ue)[e]},un=function*({allowStale:t=this.allowStale}={}){if(u(this,Ct))for(let e=u(this,Tt);!(!L(this,W,Js).call(this,e)||((t||!u(this,fe).call(this,e))&&(yield e),e===u(this,kt)));)e=u(this,zt)[e]},Js=function(t){return t!==void 0&&u(this,yt).get(u(this,lt)[t])===t},ps=function(t){var e,n;const r=u(this,Tt),s=u(this,lt)[r],a=u(this,X)[r];return u(this,Nn)&&L(this,W,at).call(this,a)?a.__abortController.abort(new Error("evicted")):(u(this,Te)||u(this,Gt))&&(u(this,Te)&&((e=u(this,De))==null||e.call(this,a,s,"evict")),u(this,Gt)&&((n=u(this,Ft))==null||n.push([a,s,"evict"]))),u(this,Hn).call(this,r),t&&(u(this,lt)[r]=void 0,u(this,X)[r]=void 0,u(this,Re).push(r)),u(this,Ct)===1?(C(this,Tt,C(this,kt,0)),u(this,Re).length=0):C(this,Tt,u(this,zt)[r]),u(this,yt).delete(s),Hs(this,Ct)._--,r},ds=function(t,e,n,r){const s=e===void 0?void 0:u(this,X)[e];if(L(this,W,at).call(this,s))return s;const a=new cs,{signal:o}=n;o?.addEventListener("abort",()=>a.abort(o.reason),{signal:a.signal});const i={signal:a.signal,options:n,context:r},l=(m,y=!1)=>{const{aborted:b}=a.signal,w=n.ignoreFetchAbort&&m!==void 0;if(n.status&&(b&&!y?(n.status.fetchAborted=!0,n.status.fetchError=a.signal.reason,w&&(n.status.fetchAbortIgnored=!0)):n.status.fetchResolved=!0),b&&!w&&!y)return h(a.signal.reason);const N=d;return u(this,X)[e]===d&&(m===void 0?N.__staleWhileFetching?u(this,X)[e]=N.__staleWhileFetching:L(this,W,hn).call(this,t,"fetch"):(n.status&&(n.status.fetchUpdated=!0),this.set(t,m,i.options))),m},c=m=>(n.status&&(n.status.fetchRejected=!0,n.status.fetchError=m),h(m)),h=m=>{const{aborted:y}=a.signal,b=y&&n.allowStaleOnFetchAbort,w=b||n.allowStaleOnFetchRejection,N=w||n.noDeleteOnFetchRejection,E=d;if(u(this,X)[e]===d&&(!N||E.__staleWhileFetching===void 0?L(this,W,hn).call(this,t,"fetch"):b||(u(this,X)[e]=E.__staleWhileFetching)),w)return n.status&&E.__staleWhileFetching!==void 0&&(n.status.returnedStale=!0),E.__staleWhileFetching;if(E.__returned===E)throw m},f=(m,y)=>{var b;const w=(b=u(this,$r))==null?void 0:b.call(this,t,s,i);w&&w instanceof Promise&&w.then(N=>m(N===void 0?void 0:N),y),a.signal.addEventListener("abort",()=>{(!n.ignoreFetchAbort||n.allowStaleOnFetchAbort)&&(m(void 0),n.allowStaleOnFetchAbort&&(m=N=>l(N,!0)))})};n.status&&(n.status.fetchDispatched=!0);const d=new Promise(f).then(l,c),p=Object.assign(d,{__abortController:a,__staleWhileFetching:s,__returned:void 0});return e===void 0?(this.set(t,p,{...i.options,status:void 0}),e=u(this,yt).get(t)):u(this,X)[e]=p,p},at=function(t){if(!u(this,Nn))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof cs},ta=function(t,e){u(this,ue)[e]=t,u(this,zt)[t]=e},Er=function(t){t!==u(this,kt)&&(t===u(this,Tt)?C(this,Tt,u(this,zt)[t]):L(this,W,ta).call(this,u(this,ue)[t],u(this,zt)[t]),L(this,W,ta).call(this,u(this,kt),t),C(this,kt,t))},hn=function(t,e){var n,r,s,a;let o=!1;if(u(this,Ct)!==0){const i=u(this,yt).get(t);if(i!==void 0)if(o=!0,u(this,Ct)===1)L(this,W,ea).call(this,e);else{u(this,Hn).call(this,i);const l=u(this,X)[i];if(L(this,W,at).call(this,l)?l.__abortController.abort(new Error("deleted")):(u(this,Te)||u(this,Gt))&&(u(this,Te)&&((n=u(this,De))==null||n.call(this,l,t,e)),u(this,Gt)&&((r=u(this,Ft))==null||r.push([l,t,e]))),u(this,yt).delete(t),u(this,lt)[i]=void 0,u(this,X)[i]=void 0,i===u(this,kt))C(this,kt,u(this,ue)[i]);else if(i===u(this,Tt))C(this,Tt,u(this,zt)[i]);else{const c=u(this,ue)[i];u(this,zt)[c]=u(this,zt)[i];const h=u(this,zt)[i];u(this,ue)[h]=u(this,ue)[i]}Hs(this,Ct)._--,u(this,Re).push(i)}}if(u(this,Gt)&&(s=u(this,Ft))!=null&&s.length){const i=u(this,Ft);let l;for(;l=i?.shift();)(a=u(this,Be))==null||a.call(this,...l)}return o},ea=function(t){var e,n,r;for(const s of L(this,W,un).call(this,{allowStale:!0})){const a=u(this,X)[s];if(L(this,W,at).call(this,a))a.__abortController.abort(new Error("deleted"));else{const o=u(this,lt)[s];u(this,Te)&&((e=u(this,De))==null||e.call(this,a,o,t)),u(this,Gt)&&((n=u(this,Ft))==null||n.push([a,o,t]))}}if(u(this,yt).clear(),u(this,X).fill(void 0),u(this,lt).fill(void 0),u(this,he)&&u(this,We)&&(u(this,he).fill(0),u(this,We).fill(0)),u(this,Oe)&&u(this,Oe).fill(0),C(this,Tt,0),C(this,kt,0),u(this,Re).length=0,C(this,ce,0),C(this,Ct,0),u(this,Gt)&&u(this,Ft)){const s=u(this,Ft);let a;for(;a=s?.shift();)(r=u(this,Be))==null||r.call(this,...a)}};let Uc=Hc;const S=t=>typeof t=="string"||t instanceof String,Un=t=>S(t)||typeof t=="number",ms="(?:0|[1-9]\\d*)",zc="clamp|max|min",Gc="exp|hypot|log|pow|sqrt",jc="abs|sign",qc="mod|rem|round",Vc="a?(?:cos|sin|tan)|atan2",xo=`${zc}|${Gc}|${jc}|${qc}|${Vc}`,na=`calc|${xo}`,Kc=`var|${na}`,zn="deg|g?rad|turn",Cr="[cm]m|[dls]?v(?:[bhiw]|max|min)|in|p[ctx]|q|r?(?:[cl]h|cap|e[mx]|ic)",jt=`[+-]?(?:${ms}(?:\\.\\d*)?|\\.\\d+)(?:e-?${ms})?`,So=`\\+?(?:${ms}(?:\\.\\d*)?|\\.\\d+)(?:e-?${ms})?`,g="none",Lt=`${jt}%`,gs=`^(?:${na})\\(|(?<=[*\\/\\s\\(])(?:${na})\\(`,Ao=`^(?:${xo})\\($`,kr="^var\\(|(?<=[*\\/\\s\\(])var\\(",Xc=`^(?:${Kc})\\(`,vs=`(?:\\s*\\/\\s*(?:${jt}|${Lt}|${g}))?`,Po=`(?:\\s*,\\s*(?:${jt}|${Lt}))?`,Mo="(?:ok)?l(?:ab|ch)|color|hsla?|hwb|rgba?",Zc="[a-z]+|#[\\da-f]{3}|#[\\da-f]{4}|#[\\da-f]{6}|#[\\da-f]{8}",Do="(?:ok)?lch|hsl|hwb",Bo="(?:de|in)creasing|longer|shorter",Yc=`${jt}(?:${zn})?`,Ro=`(?:${jt}(?:${zn})?|${g})`,Fr=`(?:${jt}|${Lt}|${g})`,Oo=`(?:${Do})(?:\\s(?:${Bo})\\shue)?`,Qc=`(${Do})(?:\\s(${Bo})\\shue)?`,Wo="(?:ok)?lab",Jc="(?:ok)?lch",tu="srgb(?:-linear)?",ra=`(?:a98|prophoto)-rgb|display-p3|rec2020|${tu}`,sa="xyz(?:-d(?:50|65))?",To=`${Wo}|${ra}|${sa}`,aa=`${Oo}|${To}`,ut="color(",Gn="color-mix(",xr=`(?:${Mo})\\(\\s*from\\s+`,eu=`(${Mo})\\(\\s*from\\s+`,oa="var(",Lo=`(?:${ra}|${sa})(?:\\s+${Fr}){3}${vs}`,Io=`^${xr}|(?<=[\\s])${xr}`,ia=`${Ro}(?:\\s+${Fr}){2}${vs}`,_o=`${Yc}(?:\\s*,\\s*${Lt}){2}${Po}`,la=`(?:${Fr}\\s+){2}${Ro}${vs}`,bs=`${Fr}(?:\\s+${Fr}){2}${vs}`,Ho=`(?:${jt}(?:\\s*,\\s*${jt}){2}|${Lt}(?:\\s*,\\s*${Lt}){2})${Po}`,jn=`${Zc}|hsla?\\(\\s*${_o}\\s*\\)|rgba?\\(\\s*${Ho}\\s*\\)|(?:hsla?|hwb)\\(\\s*${ia}\\s*\\)|(?:(?:ok)?lab|rgba?)\\(\\s*${bs}\\s*\\)|(?:ok)?lch\\(\\s*${la}\\s*\\)|color\\(\\s*${Lo}\\s*\\)`,qn=`(?:${jn})(?:\\s+${Lt})?`,ws=`color-mix\\(\\s*in\\s+(?:${aa})\\s*,\\s*${qn}\\s*,\\s*${qn}\\s*\\)`,nu=`color-mix\\(\\s*in\\s+(${aa})\\s*,\\s*(${qn})\\s*,\\s*(${qn})\\s*\\)`,ot="computedValue",Y="mixValue",rt="specifiedValue",ca="color",ua=.001,Sr=.5,ha=2,dt=3,Le=4,Ie=8,Ar=10,$s=12,T=16,ru=60,Vn=180,pe=360,q=100,_=255,En=2,Pr=3,Kn=2.4,Xn=12.92,de=.055,Mr=116,Uo=500,zo=200,fa=216/24389,ys=24389/27,Go=[.3457/.3585,1,(1-.3457-.3585)/.3585],Dr=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]],we=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]],pa=[[506752/1228815,87881/245763,12673/70218],[87098/409605,175762/245763,12673/175545],[7918/409605,87881/737289,1001167/1053270]],Ns=[[12831/3959,-329/214,-1974/3959],[-851781/878810,1648619/878810,36519/878810],[705/12673,-2585/12673,705/667]],su=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],jo=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],qo=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]],au=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],ou=[[608311/1250200,189793/714400,198249/1000160],[35783/156275,247089/357200,198249/2500400],[0/1,32229/714400,5220557/5000800]],iu=[[63426534/99577255,20160776/139408157,47086771/278816314],[26158966/99577255,472592308/697040785,8267143/139408157],[0/1,19567812/697040785,295819943/278816314]],lu=[[573536/994567,263643/1420810,187206/994567],[591459/1989134,6239551/9945670,374412/4972835],[53769/1989134,351524/4972835,4929758/4972835]],cu=[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],Vo=new RegExp(`^(?:${jn})$`),Es=new RegExp(`^${Qc}$`),uu=/^xyz(?:-d(?:50|65))?$/,wt=/^currentColor$/i,Zn=new RegExp(`^color\\(\\s*(${Lo})\\s*\\)$`),da=new RegExp(`^hsla?\\(\\s*(${ia}|${_o})\\s*\\)$`),ma=new RegExp(`^hwb\\(\\s*(${ia})\\s*\\)$`),ga=new RegExp(`^lab\\(\\s*(${bs})\\s*\\)$`),va=new RegExp(`^lch\\(\\s*(${la})\\s*\\)$`),Ko=new RegExp(`^${ws}$`),hu=new RegExp(`^${nu}$`),Xo=new RegExp(`${ws}`,"g"),ba=new RegExp(`^oklab\\(\\s*(${bs})\\s*\\)$`),wa=new RegExp(`^oklch\\(\\s*(${la})\\s*\\)$`),qt=/^(?:specifi|comput)edValue$/,Yn={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},$e=(t,e,n=!1)=>{if(e===rt){const s="";return F(t,s),s}if(n)return F(t,null),new P;const r=["rgb",0,0,0,0];return F(t,r),r},ye=(t,e=!1)=>{switch(t){case"hsl":case"hwb":case Y:return new P;case rt:return"";default:return e?new P:["rgb",0,0,0,0]}},me=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{alpha:n=!1,minLength:r=dt,maxLength:s=Le,minRange:a=0,maxRange:o=1,validateRange:i=!0}=e;if(!Number.isFinite(r))throw new TypeError(`${r} is not a number.`);if(!Number.isFinite(s))throw new TypeError(`${s} is not a number.`);if(!Number.isFinite(a))throw new TypeError(`${a} is not a number.`);if(!Number.isFinite(o))throw new TypeError(`${o} is not a number.`);const l=t.length;if(l<r||l>s)throw new Error(`Unexpected array length ${l}.`);let c=0;for(;c<l;){const h=t[c];if(Number.isFinite(h)){if(c<dt&&i&&(h<a||h>o))throw new RangeError(`${h} is not between ${a} and ${o}.`);if(c===dt&&(h<0||h>1))throw new RangeError(`${h} is not between 0 and 1.`)}else throw new TypeError(`${h} is not a number.`);c++}return n&&l===dt&&t.push(1),t},Q=(t,e,n=!1)=>{if(Array.isArray(t)){if(t.length!==dt)throw new Error(`Unexpected array length ${t.length}.`);if(!n)for(let N of t)N=me(N,{maxLength:dt,validateRange:!1})}else throw new TypeError(`${t} is not an array.`);const[[r,s,a],[o,i,l],[c,h,f]]=t;let d,p,m;n?[d,p,m]=e:[d,p,m]=me(e,{maxLength:dt,validateRange:!1});const y=r*d+s*p+a*m,b=o*d+i*p+l*m,w=c*d+h*p+f*m;return[y,b,w]},Br=(t,e,n=!1)=>{if(Array.isArray(t)){if(t.length!==Le)throw new Error(`Unexpected array length ${t.length}.`)}else throw new TypeError(`${t} is not an array.`);if(Array.isArray(e)){if(e.length!==Le)throw new Error(`Unexpected array length ${e.length}.`)}else throw new TypeError(`${e} is not an array.`);let r=0;for(;r<Le;)t[r]===g&&e[r]===g?(t[r]=0,e[r]=0):t[r]===g?t[r]=e[r]:e[r]===g&&(e[r]=t[r]),r++;if(n)return[t,e];const s=me(t,{minLength:Le,validateRange:!1}),a=me(e,{minLength:Le,validateRange:!1});return[s,a]},Rr=t=>{if(Number.isFinite(t)){if(t=Math.round(t),t<0||t>_)throw new RangeError(`${t} is not between 0 and ${_}.`)}else throw new TypeError(`${t} is not a number.`);let e=t.toString(T);return e.length===1&&(e=`0${e}`),e},Cs=t=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const e=pe/400,n=pe/(Math.PI*ha),r=new RegExp(`^(${jt})(${zn})?$`);if(!r.test(t))throw new SyntaxError(`Invalid property value: ${t}`);const[,s,a]=t.match(r);let o;switch(a){case"grad":o=parseFloat(s)*e;break;case"rad":o=parseFloat(s)*n;break;case"turn":o=parseFloat(s)*pe;break;default:o=parseFloat(s)}return o%=pe,o<0?o+=pe:Object.is(o,-0)&&(o=0),o},Ze=(t="")=>{if(S(t))if(t=t.trim(),!t)t="1";else if(t===g)t="0";else{let e;if(t.endsWith("%")?e=parseFloat(t)/q:e=parseFloat(t),!Number.isFinite(e))throw new TypeError(`${e} is not a finite number.`);e<ua?t="0":e>1?t="1":t=e.toFixed(dt)}else t="1";return parseFloat(t)},Zo=t=>{if(S(t)){if(t==="")throw new SyntaxError("Invalid property value: (empty string)");t=t.trim()}else throw new TypeError(`${t} is not a string.`);let e=parseInt(t,T);if(e<=0)return 0;if(e>=_)return 1;const n=new Map;for(let r=1;r<q;r++)n.set(Math.round(r*_/q),r);return n.has(e)?e=n.get(e)/q:e=Math.round(e/_/ua)*ua,parseFloat(e.toFixed(dt))},$a=(t,e=!1)=>{let n,r,s;e?[n,r,s]=t:[n,r,s]=me(t,{maxLength:dt,maxRange:_});let a=n/_,o=r/_,i=s/_;const l=.04045;return a>l?a=Math.pow((a+de)/(1+de),Kn):a/=Xn,o>l?o=Math.pow((o+de)/(1+de),Kn):o/=Xn,i>l?i=Math.pow((i+de)/(1+de),Kn):i/=Xn,[a,o,i]},ya=(t,e=!1)=>(e||(t=me(t,{maxLength:dt,maxRange:_})),t=$a(t,!0),Q(pa,t,!0)),Yo=(t,e=!1)=>{let[n,r,s]=me(t,{maxLength:dt});const a=809/258400;return n>a?n=Math.pow(n,1/Kn)*(1+de)-de:n*=Xn,n*=_,r>a?r=Math.pow(r,1/Kn)*(1+de)-de:r*=Xn,r*=_,s>a?s=Math.pow(s,1/Kn)*(1+de)-de:s*=Xn,s*=_,[e?Math.round(n):n,e?Math.round(r):r,e?Math.round(s):s]},Qn=(t,e=!1)=>{e||(t=me(t,{maxLength:dt,validateRange:!1}));let[n,r,s]=Q(Ns,t,!0);return[n,r,s]=Yo([Math.min(Math.max(n,0),1),Math.min(Math.max(r,0),1),Math.min(Math.max(s,0),1)],!0),[n,r,s]},Qo=(t,e=!1)=>{const[n,r,s]=Qn(t,e),a=n/_,o=r/_,i=s/_,l=Math.max(a,o,i),c=Math.min(a,o,i),h=l-c,f=(l+c)*Sr*q;let d,p;if(Math.round(f)===0||Math.round(f)===q)d=0,p=0;else if(p=h/(1-Math.abs(l+c-1))*q,p===0)d=0;else{switch(l){case a:d=(o-i)/h;break;case o:d=(i-a)/h+ha;break;case i:default:d=(a-o)/h+Le;break}d=d*ru%pe,d<0&&(d+=pe)}return[d,p,f]},fu=(t,e=!1)=>{const[n,r,s]=Qn(t,e),a=Math.min(n,r,s)/_,o=1-Math.max(n,r,s)/_;let i;return a+o===1?i=0:[i]=Qo(t),[i,a*q,o*q]},Jo=(t,e=!1)=>{e||(t=me(t,{maxLength:dt,validateRange:!1}));const n=Q(su,t,!0).map(i=>Math.cbrt(i));let[r,s,a]=Q(au,n,!0);r=Math.min(Math.max(r,0),1);const o=Math.round(parseFloat(r.toFixed(Le))*q);return(o===0||o===q)&&(s=0,a=0),[r,s,a]},pu=(t,e=!1)=>{const[n,r,s]=Jo(t,e);let a,o;const i=Math.round(parseFloat(n.toFixed(Le))*q);return i===0||i===q?(a=0,o=0):(a=Math.max(Math.sqrt(Math.pow(r,En)+Math.pow(s,En)),0),parseFloat(a.toFixed(Le))===0?o=0:(o=Math.atan2(s,r)*Vn/Math.PI,o<0&&(o+=pe))),[n,a,o]},ti=(t,e=!1)=>{e||(t=me(t,{maxLength:dt,validateRange:!1}));const n=Q(Dr,t,!0);return Qn(n,!0)},ei=(t,e=!1)=>{e||(t=me(t,{maxLength:dt,validateRange:!1}));const n=t.map((c,h)=>c/Go[h]),[r,s,a]=n.map(c=>c>fa?Math.cbrt(c):(c*ys+T)/Mr),o=Math.min(Math.max(Mr*s-T,0),q);let i,l;return o===0||o===q?(i=0,l=0):(i=(r-s)*Uo,l=(s-a)*zo),[o,i,l]},du=(t,e=!1)=>{const[n,r,s]=ei(t,e);let a,o;return n===0||n===q?(a=0,o=0):(a=Math.max(Math.sqrt(Math.pow(r,En)+Math.pow(s,En)),0),o=Math.atan2(s,r)*Vn/Math.PI,o<0&&(o+=pe)),[n,a,o]},ni=t=>{const[e,n,r,s]=me(t,{alpha:!0,maxRange:_}),a=Rr(e),o=Rr(n),i=Rr(r),l=Rr(s*_);let c;return l==="ff"?c=`#${a}${o}${i}`:c=`#${a}${o}${i}${l}`,c},Na=t=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);if(!(/^#[\da-f]{6}$/.test(t)||/^#[\da-f]{3}$/.test(t)||/^#[\da-f]{8}$/.test(t)||/^#[\da-f]{4}$/.test(t)))throw new SyntaxError(`Invalid property value: ${t}`);const e=[];if(/^#[\da-f]{3}$/.test(t)){const[,n,r,s]=t.match(/^#([\da-f])([\da-f])([\da-f])$/);e.push(parseInt(`${n}${n}`,T),parseInt(`${r}${r}`,T),parseInt(`${s}${s}`,T),1)}else if(/^#[\da-f]{4}$/.test(t)){const[,n,r,s,a]=t.match(/^#([\da-f])([\da-f])([\da-f])([\da-f])$/);e.push(parseInt(`${n}${n}`,T),parseInt(`${r}${r}`,T),parseInt(`${s}${s}`,T),Zo(`${a}${a}`))}else if(/^#[\da-f]{8}$/.test(t)){const[,n,r,s,a]=t.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);e.push(parseInt(n,T),parseInt(r,T),parseInt(s,T),Zo(a))}else{const[,n,r,s]=t.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);e.push(parseInt(n,T),parseInt(r,T),parseInt(s,T),1)}return e},mu=t=>{const[e,n,r,s]=Na(t),[a,o,i]=$a([e,n,r],!0);return[a,o,i,s]},gu=t=>{const[e,n,r,s]=mu(t),[a,o,i]=Q(pa,[e,n,r],!0);return[a,o,i,s]},ri=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e,s=new RegExp(`^rgba?\\(\\s*(${bs}|${Ho})\\s*\\)$`);if(!s.test(t)){const m=ye(n,r);return m instanceof P||S(m),m}const[,a]=t.match(s),[o,i,l,c=""]=a.replace(/[,/]/g," ").split(/\s+/);let h,f,d;o===g?h=0:(o.endsWith("%")?h=parseFloat(o)*_/q:h=parseFloat(o),h=Math.min(Math.max(B(h,Ie),0),_)),i===g?f=0:(i.endsWith("%")?f=parseFloat(i)*_/q:f=parseFloat(i),f=Math.min(Math.max(B(f,Ie),0),_)),l===g?d=0:(l.endsWith("%")?d=parseFloat(l)*_/q:d=parseFloat(l),d=Math.min(Math.max(B(d,Ie),0),_));const p=Ze(c);return["rgb",h,f,d,n===Y&&c===g?g:p]},ks=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!da.test(t)){const M=ye(n,r);return M instanceof P||S(M),M}const[,s]=t.match(da),[a,o,i,l=""]=s.replace(/[,/]/g," ").split(/\s+/);let c,h,f;a===g?c=0:c=Cs(a),o===g?h=0:h=Math.min(Math.max(parseFloat(o),0),q),i===g?f=0:f=Math.min(Math.max(parseFloat(i),0),q);const d=Ze(l);if(n==="hsl")return[n,a===g?a:c,o===g?o:h,i===g?i:f,l===g?l:d];c=c/pe*$s,f/=q;const p=h/q*Math.min(f,1-f),m=c%$s,y=(8+c)%$s,b=(4+c)%$s,w=f-p*Math.max(-1,Math.min(m-dt,dt**En-m,1)),N=f-p*Math.max(-1,Math.min(y-dt,dt**En-y,1)),E=f-p*Math.max(-1,Math.min(b-dt,dt**En-b,1));return["rgb",Math.min(Math.max(B(w*_,Ie),0),_),Math.min(Math.max(B(N*_,Ie),0),_),Math.min(Math.max(B(E*_,Ie),0),_),d]},Ea=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!ma.test(t)){const w=ye(n,r);return w instanceof P||S(w),w}const[,s]=t.match(ma),[a,o,i,l=""]=s.replace("/"," ").split(/\s+/);let c,h,f;a===g?c=0:c=Cs(a),o===g?h=0:h=Math.min(Math.max(parseFloat(o),0),q)/q,i===g?f=0:f=Math.min(Math.max(parseFloat(i),0),q)/q;const d=Ze(l);if(n==="hwb")return[n,a===g?a:c,o===g?o:h*q,i===g?i:f*q,l===g?l:d];if(h+f>=1){const w=B(h/(h+f)*_,Ie);return["rgb",w,w,w,d]}const p=(1-h-f)/_;let[,m,y,b]=ks(`hsl(${c} 100 50)`);return m=B((m*p+h)*_,Ie),y=B((y*p+h)*_,Ie),b=B((b*p+h)*_,Ie),["rgb",Math.min(Math.max(m,0),_),Math.min(Math.max(y,0),_),Math.min(Math.max(b,0),_),d]},Or=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!ga.test(t)){const U=ye(n,r);return U instanceof P||S(U),U}const s=1.25,a=8,[,o]=t.match(ga),[i,l,c,h=""]=o.replace("/"," ").split(/\s+/);let f,d,p;i===g?f=0:(i.endsWith("%")?(f=parseFloat(i),f>q&&(f=q)):f=parseFloat(i),f<0&&(f=0)),l===g?d=0:d=l.endsWith("%")?parseFloat(l)*s:parseFloat(l),c===g?p=0:p=c.endsWith("%")?parseFloat(c)*s:parseFloat(c);const m=Ze(h);if(qt.test(n))return["lab",i===g?i:B(f,T),l===g?l:B(d,T),c===g?c:B(p,T),h===g?h:m];const y=(f+T)/Mr,b=d/Uo+y,w=y-p/zo,N=Math.pow(y,Pr),E=Math.pow(b,Pr),M=Math.pow(w,Pr),k=[E>fa?E:(b*Mr-T)/ys,f>a?N:f/ys,M>fa?M:(w*Mr-T)/ys],[O,A,R]=k.map((U,j)=>U*Go[j]);return["xyz-d50",B(O,T),B(A,T),B(R,T),m]},Fs=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!va.test(t)){const E=ye(n,r);return E instanceof P||S(E),E}const s=1.5,[,a]=t.match(va),[o,i,l,c=""]=a.replace("/"," ").split(/\s+/);let h,f,d;o===g?h=0:(h=parseFloat(o),h<0&&(h=0)),i===g?f=0:f=i.endsWith("%")?parseFloat(i)*s:parseFloat(i),l===g?d=0:d=Cs(l);const p=Ze(c);if(qt.test(n))return["lch",o===g?o:B(h,T),i===g?i:B(f,T),l===g?l:B(d,T),c===g?c:p];const m=f*Math.cos(d*Math.PI/Vn),y=f*Math.sin(d*Math.PI/Vn),[,b,w,N]=Or(`lab(${h} ${m} ${y})`);return["xyz-d50",B(b,T),B(w,T),B(N,T),p]},xs=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!ba.test(t)){const N=ye(n,r);return N instanceof P||S(N),N}const s=.4,[,a]=t.match(ba),[o,i,l,c=""]=a.replace("/"," ").split(/\s+/);let h,f,d;o===g?h=0:(h=o.endsWith("%")?parseFloat(o)/q:parseFloat(o),h<0&&(h=0)),i===g?f=0:i.endsWith("%")?f=parseFloat(i)*s/q:f=parseFloat(i),l===g?d=0:l.endsWith("%")?d=parseFloat(l)*s/q:d=parseFloat(l);const p=Ze(c);if(qt.test(n))return["oklab",o===g?o:B(h,T),i===g?i:B(f,T),l===g?l:B(d,T),c===g?c:p];const m=Q(qo,[h,f,d]).map(N=>Math.pow(N,Pr)),[y,b,w]=Q(jo,m,!0);return["xyz-d65",B(y,T),B(b,T),B(w,T),p]},Ss=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!wa.test(t)){const M=ye(n,r);return M instanceof P||S(M),M}const s=.4,[,a]=t.match(wa),[o,i,l,c=""]=a.replace("/"," ").split(/\s+/);let h,f,d;o===g?h=0:(h=o.endsWith("%")?parseFloat(o)/q:parseFloat(o),h<0&&(h=0)),i===g?f=0:(i.endsWith("%")?f=parseFloat(i)*s/q:f=parseFloat(i),f<0&&(f=0)),l===g?d=0:d=Cs(l);const p=Ze(c);if(qt.test(n))return["oklch",o===g?o:B(h,T),i===g?i:B(f,T),l===g?l:B(d,T),c===g?c:p];const m=f*Math.cos(d*Math.PI/Vn),y=f*Math.sin(d*Math.PI/Vn),b=Q(qo,[h,m,y]).map(M=>Math.pow(M,Pr)),[w,N,E]=Q(jo,b,!0);return["xyz-d65",B(w,T),B(N,T),B(E,T),p]},bt=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",d50:r=!1,format:s="",nullable:a=!1}=e;if(!Zn.test(t)){const E=ye(s,a);return E instanceof P||S(E),E}const[,o]=t.match(Zn);let[i,l,c,h,f=""]=o.replace("/"," ").split(/\s+/),d,p,m;i==="xyz"&&(i="xyz-d65"),l===g?d=0:d=l.endsWith("%")?parseFloat(l)/q:parseFloat(l),c===g?p=0:p=c.endsWith("%")?parseFloat(c)/q:parseFloat(c),h===g?m=0:m=h.endsWith("%")?parseFloat(h)/q:parseFloat(h);const y=Ze(f);if(qt.test(s)||s===Y&&i===n)return[i,l===g?l:B(d,Ar),c===g?c:B(p,Ar),h===g?h:B(m,Ar),f===g?f:y];let b=0,w=0,N=0;if(i==="srgb-linear")[b,w,N]=Q(pa,[d,p,m]),r&&([b,w,N]=Q(we,[b,w,N],!0));else if(i==="display-p3"){const E=$a([d*_,p*_,m*_]);[b,w,N]=Q(ou,E),r&&([b,w,N]=Q(we,[b,w,N],!0))}else if(i==="rec2020"){const E=1.09929682680944,M=.018053968510807,k=.45,O=[d,p,m].map(A=>{let R;return A<M*k*Ar?R=A/(k*Ar):R=Math.pow((A+E-1)/E,1/k),R});[b,w,N]=Q(iu,O),r&&([b,w,N]=Q(we,[b,w,N],!0))}else if(i==="a98-rgb"){const E=2.19921875,M=[d,p,m].map(k=>Math.pow(k,E));[b,w,N]=Q(lu,M),r&&([b,w,N]=Q(we,[b,w,N],!0))}else if(i==="prophoto-rgb"){const E=[d,p,m].map(M=>{let k;return M>1/(T*ha)?k=Math.pow(M,1.8):k=M/T,k});[b,w,N]=Q(cu,E),r||([b,w,N]=Q(Dr,[b,w,N],!0))}else/^xyz(?:-d(?:50|65))?$/.test(i)?([b,w,N]=[d,p,m],i==="xyz-d50"?r||([b,w,N]=Q(Dr,[b,w,N])):r&&([b,w,N]=Q(we,[b,w,N],!0))):([b,w,N]=ya([d*_,p*_,m*_]),r&&([b,w,N]=Q(we,[b,w,N],!0)));return[r?"xyz-d50":"xyz-d65",B(b,T),B(w,T),B(N,T),s===Y&&f===g?f:y]},Nt=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{d50:n=!1,format:r="",nullable:s=!1}=e;if(!Vo.test(t)){const c=ye(r,s);return c instanceof P||S(c),c}let a=0,o=0,i=0,l=0;if(wt.test(t)){if(r===ot)return["rgb",0,0,0,0];if(r===rt)return t}else if(/^[a-z]+$/.test(t))if(Object.prototype.hasOwnProperty.call(Yn,t)){if(r===rt)return t;const[c,h,f]=Yn[t];if(l=1,r===ot)return["rgb",c,h,f,l];[a,o,i]=ya([c,h,f],!0),n&&([a,o,i]=Q(we,[a,o,i],!0))}else switch(r){case ot:return s&&t!=="transparent"?new P:["rgb",0,0,0,0];case rt:return t==="transparent"?t:"";case Y:return t==="transparent"?["rgb",0,0,0,0]:new P}else if(t[0]==="#"){if(qt.test(r))return["rgb",...Na(t)];[a,o,i,l]=gu(t),n&&([a,o,i]=Q(we,[a,o,i],!0))}else if(t.startsWith("lab")){if(qt.test(r))return Or(t,e);[,a,o,i,l]=Or(t),n||([a,o,i]=Q(Dr,[a,o,i],!0))}else if(t.startsWith("lch")){if(qt.test(r))return Fs(t,e);[,a,o,i,l]=Fs(t),n||([a,o,i]=Q(Dr,[a,o,i],!0))}else if(t.startsWith("oklab")){if(qt.test(r))return xs(t,e);[,a,o,i,l]=xs(t),n&&([a,o,i]=Q(we,[a,o,i],!0))}else if(t.startsWith("oklch")){if(qt.test(r))return Ss(t,e);[,a,o,i,l]=Ss(t),n&&([a,o,i]=Q(we,[a,o,i],!0))}else{let c,h,f;if(t.startsWith("hsl")?[,c,h,f,l]=ks(t):t.startsWith("hwb")?[,c,h,f,l]=Ea(t):[,c,h,f,l]=ri(t,e),qt.test(r))return["rgb",Math.round(c),Math.round(h),Math.round(f),l];[a,o,i]=ya([c,h,f]),n&&([a,o,i]=Q(we,[a,o,i],!0))}return[n?"xyz-d50":"xyz-d65",B(a,T),B(o,T),B(i,T),l]},Cn=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r="",nullable:s=!1}=e,a=gt({namespace:ca,name:"resolveColorValue",value:t},e),o=mt(a);if(o instanceof st){if(o.isNull)return o;const p=o.item;return S(p),p}if(!Vo.test(t)){const p=ye(r,s);return p instanceof P?(F(a,null),p):(F(a,p),S(p),p)}let i="",l=0,c=0,h=0,f=0;if(wt.test(t)){if(r===rt)return F(a,t),t}else if(/^[a-z]+$/.test(t))if(Object.prototype.hasOwnProperty.call(Yn,t)){if(r===rt)return F(a,t),t;[l,c,h]=Yn[t],f=1}else switch(r){case rt:{if(t==="transparent")return F(a,t),t;const p="";return F(a,p),p}case Y:{if(t==="transparent"){const p=["rgb",0,0,0,0];return F(a,p),p}return F(a,null),new P}case ot:default:{if(s&&t!=="transparent")return F(a,null),new P;const p=["rgb",0,0,0,0];return F(a,p),p}}else if(t[0]==="#")[l,c,h,f]=Na(t);else if(t.startsWith("hsl"))[,l,c,h,f]=ks(t,e);else if(t.startsWith("hwb"))[,l,c,h,f]=Ea(t,e);else if(/^l(?:ab|ch)/.test(t)){let p,m,y;if(t.startsWith("lab")?[i,p,m,y,f]=Or(t,e):[i,p,m,y,f]=Fs(t,e),qt.test(r)){const b=[i,p,m,y,f];return F(a,b),b}[l,c,h]=ti([p,m,y])}else if(/^okl(?:ab|ch)/.test(t)){let p,m,y;if(t.startsWith("oklab")?[i,p,m,y,f]=xs(t,e):[i,p,m,y,f]=Ss(t,e),qt.test(r)){const b=[i,p,m,y,f];return F(a,b),b}[l,c,h]=Qn([p,m,y])}else[,l,c,h,f]=ri(t,e);if(r===Y&&n==="srgb"){const p=["srgb",l/_,c/_,h/_,f];return F(a,p),p}const d=["rgb",Math.round(l),Math.round(c),Math.round(h),f];return F(a,d),d},Ye=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r="",nullable:s=!1}=e,a=gt({namespace:ca,name:"resolveColorFunc",value:t},e),o=mt(a);if(o instanceof st){if(o.isNull)return o;const M=o.item;return S(M),M}if(!Zn.test(t)){const M=ye(r,s);return M instanceof P?(F(a,null),M):(F(a,M),S(M),M)}const[i,l,c,h,f]=bt(t,e);if(qt.test(r)||r===Y&&i===n){const M=[i,l,c,h,f];return F(a,M),M}const d=parseFloat(`${l}`),p=parseFloat(`${c}`),m=parseFloat(`${h}`),y=Ze(`${f}`),[b,w,N]=Qn([d,p,m],!0),E=["rgb",b,w,N,y];return F(a,E),E},Ca=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r=""}=e;let s="",a,o,i,l,c,h,f;if(r===Y){let d;if(t.startsWith(ut)?d=bt(t,e):d=Nt(t,e),d instanceof P)return d;if([s,c,h,f,l]=d,s===n)return[c,h,f,l];[a,o,i]=Q(Ns,[c,h,f],!0)}else if(t.startsWith(ut)){const[,d]=t.match(Zn),[p]=d.replace("/"," ").split(/\s+/);p==="srgb-linear"?[,a,o,i,l]=Ye(t,{format:ot}):([,c,h,f,l]=bt(t),[a,o,i]=Q(Ns,[c,h,f],!0))}else[,c,h,f,l]=Nt(t),[a,o,i]=Q(Ns,[c,h,f],!0);return[Math.min(Math.max(a,0),1),Math.min(Math.max(o,0),1),Math.min(Math.max(i,0),1),l]},Wr=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(n===Y){let i;if(t.startsWith(ut)?i=Ye(t,e):i=Cn(t,e),i instanceof P)return i;[,r,s,a,o]=i}else if(t.startsWith(ut)){const[,i]=t.match(Zn),[l]=i.replace("/"," ").split(/\s+/);l==="srgb"?([,r,s,a,o]=Ye(t,{format:ot}),r*=_,s*=_,a*=_):[,r,s,a,o]=Ye(t)}else/^(?:ok)?l(?:ab|ch)/.test(t)?([r,s,a,o]=Ca(t),[r,s,a]=Yo([r,s,a])):[,r,s,a,o]=Cn(t,{format:ot});return[r,s,a,o]},si=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{d50:n=!1,format:r=""}=e;let s,a,o,i;if(r===Y){let l;if(t.startsWith(ut)?l=bt(t,e):l=Nt(t,e),l instanceof P)return l;[,s,a,o,i]=l}else if(t.startsWith(ut)){const[,l]=t.match(Zn),[c]=l.replace("/"," ").split(/\s+/);n?c==="xyz-d50"?[,s,a,o,i]=Ye(t,{format:ot}):[,s,a,o,i]=bt(t,e):/^xyz(?:-d65)?$/.test(c)?[,s,a,o,i]=Ye(t,{format:ot}):[,s,a,o,i]=bt(t)}else[,s,a,o,i]=Nt(t,e);return[s,a,o,i]},ka=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(da.test(t))return[,r,s,a,o]=ks(t,{format:"hsl"}),n==="hsl"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o];let i,l,c;if(n===Y){let h;if(t.startsWith(ut)?h=bt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,l,c,o]=h}else t.startsWith(ut)?[,i,l,c,o]=bt(t):[,i,l,c,o]=Nt(t);return[r,s,a]=Qo([i,l,c],!0),n==="hsl"?[Math.round(r),Math.round(s),Math.round(a),o]:[n===Y&&s===0?g:r,s,a,o]},Fa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(ma.test(t))return[,r,s,a,o]=Ea(t,{format:"hwb"}),n==="hwb"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o];let i,l,c;if(n===Y){let h;if(t.startsWith(ut)?h=bt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,l,c,o]=h}else t.startsWith(ut)?[,i,l,c,o]=bt(t):[,i,l,c,o]=Nt(t);return[r,s,a]=fu([i,l,c],!0),n==="hwb"?[Math.round(r),Math.round(s),Math.round(a),o]:[n===Y&&s+a>=100?g:r,s,a,o]},xa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(ga.test(t))return[,r,s,a,o]=Or(t,{format:ot}),[r,s,a,o];let i,l,c;if(n===Y){let h;if(e.d50=!0,t.startsWith(ut)?h=bt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,l,c,o]=h}else t.startsWith(ut)?[,i,l,c,o]=bt(t,{d50:!0}):[,i,l,c,o]=Nt(t,{d50:!0});return[r,s,a]=ei([i,l,c],!0),[r,s,a,o]},Sa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(va.test(t))return[,r,s,a,o]=Fs(t,{format:ot}),[r,s,a,o];let i,l,c;if(n===Y){let h;if(e.d50=!0,t.startsWith(ut)?h=bt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,l,c,o]=h}else t.startsWith(ut)?[,i,l,c,o]=bt(t,{d50:!0}):[,i,l,c,o]=Nt(t,{d50:!0});return[r,s,a]=du([i,l,c],!0),[r,s,n===Y&&s===0?g:a,o]},Aa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(ba.test(t))return[,r,s,a,o]=xs(t,{format:ot}),[r,s,a,o];let i,l,c;if(n===Y){let h;if(t.startsWith(ut)?h=bt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,l,c,o]=h}else t.startsWith(ut)?[,i,l,c,o]=bt(t):[,i,l,c,o]=Nt(t);return[r,s,a]=Jo([i,l,c],!0),[r,s,a,o]},Pa=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(wa.test(t))return[,r,s,a,o]=Ss(t,{format:ot}),[r,s,a,o];let i,l,c;if(n===Y){let h;if(t.startsWith(ut)?h=bt(t,e):h=Nt(t,e),h instanceof P)return h;[,i,l,c,o]=h}else t.startsWith(ut)?[,i,l,c,o]=bt(t):[,i,l,c,o]=Nt(t);return[r,s,a]=pu([i,l,c],!0),[r,s,n===Y&&s===0?g:a,o]},As=(t,e={})=>{if(S(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e,s=gt({namespace:ca,name:"resolveColorMix",value:t},e),a=mt(s);if(a instanceof st){if(a.isNull)return a;const k=a.item;return S(k),k}const o=[];if(!Ko.test(t))if(t.startsWith(Gn)&&Xo.test(t)){const k=new RegExp(`^(?:${ra}|${sa})$`),O=t.match(Xo);for(const A of O)if(A){let R=As(A,{format:n===rt?n:ot});if(Array.isArray(R)){const[U,j,Z,J,ct]=R;if(j===0&&Z===0&&J===0&&ct===0){t="";break}k.test(U)?ct===1?R=`color(${U} ${j} ${Z} ${J})`:R=`color(${U} ${j} ${Z} ${J} / ${ct})`:ct===1?R=`${U}(${j} ${Z} ${J})`:R=`${U}(${j} ${Z} ${J} / ${ct})`}else if(!Ko.test(R)){t="";break}o.push(R),t=t.replace(A,R)}if(!t)return $e(s,n,r)}else return $e(s,n,r);let i="",l="",c="",h="",f="",d="";if(o.length&&n===rt){const k=new RegExp(`^color-mix\\(\\s*in\\s+(${aa})\\s*,`),[,O]=t.match(k);if(Es.test(O)?[,i,l]=O.match(Es):i=O,o.length===2){let[A,R]=o;A=A.replace(/(?=[()])/g,"\\"),R=R.replace(/(?=[()])/g,"\\");const U=new RegExp(`(${A})(?:\\s+(${Lt}))?`),j=new RegExp(`(${R})(?:\\s+(${Lt}))?`);[,c,h]=t.match(U),[,f,d]=t.match(j)}else{let[A]=o;A=A.replace(/(?=[()])/g,"\\");const R=`${A}(?:\\s+${Lt})?`,U=`(${A})(?:\\s+(${Lt}))?`,j=new RegExp(`^${U}$`),Z=new RegExp(`${U}\\s*\\)$`),J=new RegExp(`^(${jn})(?:\\s+(${Lt}))?$`);if(Z.test(t)){const ct=new RegExp(`(${qn})\\s*,\\s*(${R})\\s*\\)$`),[,Et,Kt]=t.match(ct);[,c,h]=Et.match(J),[,f,d]=Kt.match(j)}else{const ct=new RegExp(`(${R})\\s*,\\s*(${qn})\\s*\\)$`),[,Et,Kt]=t.match(ct);[,c,h]=Et.match(j),[,f,d]=Kt.match(J)}}}else{const[,k,O,A]=t.match(hu),R=new RegExp(`^(${jn})(?:\\s+(${Lt}))?$`);[,c,h]=O.match(R),[,f,d]=A.match(R),Es.test(k)?[,i,l]=k.match(Es):i=k}let p,m,y;if(h&&d){const k=parseFloat(h)/q,O=parseFloat(d)/q;if(k<0||k>1||O<0||O>1)return $e(s,n,r);const A=k+O;if(A===0)return $e(s,n,r);p=k/A,m=O/A,y=A<1?A:1}else{if(h){if(p=parseFloat(h)/q,p<0||p>1)return $e(s,n,r);m=1-p}else if(d){if(m=parseFloat(d)/q,m<0||m>1)return $e(s,n,r);p=1-m}else p=Sr,m=Sr;y=1}if(i==="xyz"&&(i="xyz-d65"),n===rt){let k="",O="";if(c.startsWith(Gn))k=c;else if(c.startsWith(ut)){const[A,R,U,j,Z]=bt(c,e);Z===1?k=`color(${A} ${R} ${U} ${j})`:k=`color(${A} ${R} ${U} ${j} / ${Z})`}else{const A=Nt(c,e);if(Array.isArray(A)){const[R,U,j,Z,J]=A;J===1?R==="rgb"?k=`${R}(${U}, ${j}, ${Z})`:k=`${R}(${U} ${j} ${Z})`:R==="rgb"?k=`${R}a(${U}, ${j}, ${Z}, ${J})`:k=`${R}(${U} ${j} ${Z} / ${J})`}else{if(!S(A)||!A)return F(s,""),"";k=A}}if(f.startsWith(Gn))O=f;else if(f.startsWith(ut)){const[A,R,U,j,Z]=bt(f,e);Z===1?O=`color(${A} ${R} ${U} ${j})`:O=`color(${A} ${R} ${U} ${j} / ${Z})`}else{const A=Nt(f,e);if(Array.isArray(A)){const[R,U,j,Z,J]=A;J===1?R==="rgb"?O=`${R}(${U}, ${j}, ${Z})`:O=`${R}(${U} ${j} ${Z})`:R==="rgb"?O=`${R}a(${U}, ${j}, ${Z}, ${J})`:O=`${R}(${U} ${j} ${Z} / ${J})`}else{if(!S(A)||!A)return F(s,""),"";O=A}}if(h&&d)k+=` ${parseFloat(h)}%`,O+=` ${parseFloat(d)}%`;else if(h){const A=parseFloat(h);A!==q*Sr&&(k+=` ${A}%`)}else if(d){const A=q-parseFloat(d);A!==q*Sr&&(k+=` ${A}%`)}if(l){const A=`color-mix(in ${i} ${l} hue, ${k}, ${O})`;return F(s,A),A}else{const A=`color-mix(in ${i}, ${k}, ${O})`;return F(s,A),A}}let b=0,w=0,N=0,E=0;if(/^srgb(?:-linear)?$/.test(i)){let k,O;if(i==="srgb"?(wt.test(c)?k=[g,g,g,g]:k=Wr(c,{colorSpace:i,format:Y}),wt.test(f)?O=[g,g,g,g]:O=Wr(f,{colorSpace:i,format:Y})):(wt.test(c)?k=[g,g,g,g]:k=Ca(c,{colorSpace:i,format:Y}),wt.test(f)?O=[g,g,g,g]:O=Ca(f,{colorSpace:i,format:Y})),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,j]=k,[Z,J,ct,Et]=O,Kt=A===g&&Z===g,ve=R===g&&J===g,ke=U===g&&ct===g,Fe=j===g&&Et===g,[[Xt,Mt,Dt,xe],[Zt,Bt,xt,je]]=Br([A,R,U,j],[Z,J,ct,Et],!0),ht=xe*p,ft=je*m;if(E=ht+ft,E===0?(b=Xt*p+Zt*m,w=Mt*p+Bt*m,N=Dt*p+xt*m):(b=(Xt*ht+Zt*ft)/E,w=(Mt*ht+Bt*ft)/E,N=(Dt*ht+xt*ft)/E,E=parseFloat(E.toFixed(3))),n===ot){const $t=[i,Kt?g:B(b,T),ve?g:B(w,T),ke?g:B(N,T),Fe?g:E*y];return F(s,$t),$t}b*=_,w*=_,N*=_}else if(uu.test(i)){let k,O;if(wt.test(c)?k=[g,g,g,g]:k=si(c,{colorSpace:i,d50:i==="xyz-d50",format:Y}),wt.test(f)?O=[g,g,g,g]:O=si(f,{colorSpace:i,d50:i==="xyz-d50",format:Y}),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,j]=k,[Z,J,ct,Et]=O,Kt=A===g&&Z===g,ve=R===g&&J===g,ke=U===g&&ct===g,Fe=j===g&&Et===g,[[Xt,Mt,Dt,xe],[Zt,Bt,xt,je]]=Br([A,R,U,j],[Z,J,ct,Et],!0),ht=xe*p,ft=je*m;E=ht+ft;let $t,Rt,Ot;if(E===0?($t=Xt*p+Zt*m,Rt=Mt*p+Bt*m,Ot=Dt*p+xt*m):($t=(Xt*ht+Zt*ft)/E,Rt=(Mt*ht+Bt*ft)/E,Ot=(Dt*ht+xt*ft)/E,E=parseFloat(E.toFixed(3))),n===ot){const sn=[i,Kt?g:B($t,T),ve?g:B(Rt,T),ke?g:B(Ot,T),Fe?g:E*y];return F(s,sn),sn}i==="xyz-d50"?[b,w,N]=ti([$t,Rt,Ot],!0):[b,w,N]=Qn([$t,Rt,Ot],!0)}else if(/^h(?:sl|wb)$/.test(i)){let k,O;if(i==="hsl"?(wt.test(c)?k=[g,g,g,g]:k=ka(c,{colorSpace:i,format:Y}),wt.test(f)?O=[g,g,g,g]:O=ka(f,{colorSpace:i,format:Y})):(wt.test(c)?k=[g,g,g,g]:k=Fa(c,{colorSpace:i,format:Y}),wt.test(f)?O=[g,g,g,g]:O=Fa(f,{colorSpace:i,format:Y})),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,j]=k,[Z,J,ct,Et]=O,Kt=j===g&&Et===g;let[[ve,ke,Fe,Xt],[Mt,Dt,xe,Zt]]=Br([A,R,U,j],[Z,J,ct,Et],!0);l&&([ve,Mt]=Hi(ve,Mt,l));const Bt=Xt*p,xt=Zt*m;E=Bt+xt;const je=(ve*p+Mt*m)%pe;let ht,ft;if(E===0?(ht=ke*p+Dt*m,ft=Fe*p+xe*m):(ht=(ke*Bt+Dt*xt)/E,ft=(Fe*Bt+xe*xt)/E,E=parseFloat(E.toFixed(3))),[b,w,N]=Wr(`${i}(${je} ${ht} ${ft})`),n===ot){const $t=["srgb",B(b/_,T),B(w/_,T),B(N/_,T),Kt?g:E*y];return F(s,$t),$t}}else if(/^(?:ok)?lch$/.test(i)){let k,O;if(i==="lch"?(wt.test(c)?k=[g,g,g,g]:k=Sa(c,{colorSpace:i,format:Y}),wt.test(f)?O=[g,g,g,g]:O=Sa(f,{colorSpace:i,format:Y})):(wt.test(c)?k=[g,g,g,g]:k=Pa(c,{colorSpace:i,format:Y}),wt.test(f)?O=[g,g,g,g]:O=Pa(f,{colorSpace:i,format:Y})),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,j]=k,[Z,J,ct,Et]=O,Kt=A===g&&Z===g,ve=R===g&&J===g,ke=U===g&&ct===g,Fe=j===g&&Et===g;let[[Xt,Mt,Dt,xe],[Zt,Bt,xt,je]]=Br([A,R,U,j],[Z,J,ct,Et],!0);l&&([Dt,xt]=Hi(Dt,xt,l));const ht=xe*p,ft=je*m;E=ht+ft;const $t=(Dt*p+xt*m)%pe;let Rt,Ot;if(E===0?(Rt=Xt*p+Zt*m,Ot=Mt*p+Bt*m):(Rt=(Xt*ht+Zt*ft)/E,Ot=(Mt*ht+Bt*ft)/E,E=parseFloat(E.toFixed(3))),n===ot){const sn=[i,Kt?g:B(Rt,T),ve?g:B(Ot,T),ke?g:B($t,T),Fe?g:E*y];return F(s,sn),sn}[,b,w,N]=Cn(`${i}(${Rt} ${Ot} ${$t})`)}else{let k,O;if(i==="lab"?(wt.test(c)?k=[g,g,g,g]:k=xa(c,{colorSpace:i,format:Y}),wt.test(f)?O=[g,g,g,g]:O=xa(f,{colorSpace:i,format:Y})):(wt.test(c)?k=[g,g,g,g]:k=Aa(c,{colorSpace:i,format:Y}),wt.test(f)?O=[g,g,g,g]:O=Aa(f,{colorSpace:i,format:Y})),k instanceof P||O instanceof P)return $e(s,n,r);const[A,R,U,j]=k,[Z,J,ct,Et]=O,Kt=A===g&&Z===g,ve=R===g&&J===g,ke=U===g&&ct===g,Fe=j===g&&Et===g,[[Xt,Mt,Dt,xe],[Zt,Bt,xt,je]]=Br([A,R,U,j],[Z,J,ct,Et],!0),ht=xe*p,ft=je*m;E=ht+ft;let $t,Rt,Ot;if(E===0?($t=Xt*p+Zt*m,Rt=Mt*p+Bt*m,Ot=Dt*p+xt*m):($t=(Xt*ht+Zt*ft)/E,Rt=(Mt*ht+Bt*ft)/E,Ot=(Dt*ht+xt*ft)/E,E=parseFloat(E.toFixed(3))),n===ot){const sn=[i,Kt?g:B($t,T),ve?g:B(Rt,T),ke?g:B(Ot,T),Fe?g:E*y];return F(s,sn),sn}[,b,w,N]=Cn(`${i}(${$t} ${Rt} ${Ot})`)}const M=["rgb",Math.round(b),Math.round(w),Math.round(N),parseFloat((E*y).toFixed(3))];return F(s,M),M},{CloseParen:ai,Comment:vu,EOF:bu,Ident:wu,Whitespace:$u}=v,yu="css-var",oi=new RegExp(gs),ii=new RegExp(kr);function li(t,e={}){if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{customProperty:n={}}=e,r=[];for(;t.length;){const o=t.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i,l]=o;if(i===ai)break;if(l===oa){const[c,h]=li(t,e);t=c,h&&r.push(h)}else if(i===wu)if(l.startsWith("--")){let c;Object.hasOwnProperty.call(n,l)?c=n[l]:typeof n.callback=="function"&&(c=n.callback(l)),c&&r.push(c)}else l&&r.push(l)}let s=!1;if(r.length>1){const o=r[r.length-1];s=fn(o)}let a="";for(let o of r){if(o=o.trim(),ii.test(o)){const i=Tr(o,e);S(i)&&(s?fn(i)&&(a=i):a=i)}else oi.test(o)?(o=jr(o,e),s?fn(o)&&(a=o):a=o):o&&!/^(?:inherit|initial|revert(?:-layer)?|unset)$/.test(o)&&(s?fn(o)&&(a=o):a=o);if(a)break}return[t,a]}function Nu(t,e={}){const n=[];for(;t.length;){const r=t.shift(),[s="",a=""]=r;if(a===oa){const[o,i]=li(t,e);if(!i)return new P;t=o,n.push(i)}else switch(s){case ai:{n.length&&n[n.length-1]===" "?n.splice(-1,1,a):n.push(a);break}case $u:{if(n.length){const o=n[n.length-1];S(o)&&!o.endsWith("(")&&o!==" "&&n.push(a)}break}default:s!==vu&&s!==bu&&n.push(a)}}return n}function Tr(t,e={}){const{format:n=""}=e;if(S(t)){if(!ii.test(t)||n===rt)return t;t=t.trim()}else throw new TypeError(`${t} is not a string.`);const r=gt({namespace:yu,name:"resolveVar",value:t},e),s=mt(r);if(s instanceof st)return s.isNull?s:s.item;const a=Ve({css:t}),o=Nu(a,e);if(Array.isArray(o)){let i=o.join("");return oi.test(i)&&(i=jr(i,e)),F(r,i),i}else return F(r,null),new P}const Eu=(t,e={})=>{const n=Tr(t,e);return S(n)?n:""};function At(t,e){return[t[0]*e[0]+t[1]*e[1]+t[2]*e[2],t[3]*e[0]+t[4]*e[1]+t[5]*e[2],t[6]*e[0]+t[7]*e[1]+t[8]*e[2]]}const Cu=[.955473421488075,-.02309845494876471,.06325924320057072,-.0283697093338637,1.0099953980813041,.021041441191917323,.012314014864481998,-.020507649298898964,1.330365926242124];/**
 * Bradford chromatic adaptation from D50 to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function _e(t){return At(Cu,t)}const ku=[1.0479297925449969,.022946870601609652,-.05019226628920524,.02962780877005599,.9904344267538799,-.017073799063418826,-.009243040646204504,.015055191490298152,.7518742814281371];/**
 * Bradford chromatic adaptation from D65 to D50
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html
 */function He(t){return At(ku,t)}/**
 * @param {number} hue - Hue as degrees 0..360
 * @param {number} sat - Saturation as percentage 0..100
 * @param {number} light - Lightness as percentage 0..100
 * @return {number[]} Array of sRGB components; in-gamut colors in range [0..1]
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js
 */function ci(t){let e=t[0]%360;const n=t[1]/100,r=t[2]/100;return e<0&&(e+=360),[Ma(0,e,n,r),Ma(8,e,n,r),Ma(4,e,n,r)]}function Ma(t,e,n,r){const s=(t+e/30)%12;return r-n*Math.min(r,1-r)*Math.max(-1,Math.min(s-3,9-s,1))}/**
 * @param {number} hue -  Hue as degrees 0..360
 * @param {number} white -  Whiteness as percentage 0..100
 * @param {number} black -  Blackness as percentage 0..100
 * @return {number[]} Array of RGB components 0..1
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js
 */function Fu(t){const e=t[0],n=t[1]/100,r=t[2]/100;if(n+r>=1){const o=n/(n+r);return[o,o,o]}const s=ci([e,100,50]),a=1-n-r;return[s[0]*a+n,s[1]*a+n,s[2]*a+n]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function xu(t){const e=t[2]*Math.PI/180;return[t[0],t[1]*Math.cos(e),t[1]*Math.sin(e)]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Su(t){const e=180*Math.atan2(t[2],t[1])/Math.PI;return[t[0],Math.sqrt(Math.pow(t[1],2)+Math.pow(t[2],2)),e>=0?e:e+360]}const Jn=[.3457/.3585,1,.2958/.3585];/**
 * Convert Lab to D50-adapted XYZ
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */function ui(t){const e=903.2962962962963,n=216/24389,r=(t[0]+16)/116,s=t[1]/500+r,a=r-t[2]/200;return[(Math.pow(s,3)>n?Math.pow(s,3):(116*s-16)/e)*Jn[0],(t[0]>8?Math.pow((t[0]+16)/116,3):t[0]/e)*Jn[1],(Math.pow(a,3)>n?Math.pow(a,3):(116*a-16)/e)*Jn[2]]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function hi(t){const e=t[2]*Math.PI/180;return[t[0],t[1]*Math.cos(e),t[1]*Math.sin(e)]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function fi(t){const e=180*Math.atan2(t[2],t[1])/Math.PI;return[t[0],Math.sqrt(t[1]**2+t[2]**2),e>=0?e:e+360]}const Au=[1.2268798758459243,-.5578149944602171,.2813910456659647,-.0405757452148008,1.112286803280317,-.0717110580655164,-.0763729366746601,-.4214933324022432,1.5869240198367816],Pu=[1,.3963377773761749,.2158037573099136,1,-.1055613458156586,-.0638541728258133,1,-.0894841775298119,-1.2914855480194092];/**
 * Given OKLab, convert to XYZ relative to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function Da(t){const e=At(Pu,t);return At(Au,[e[0]**3,e[1]**3,e[2]**3])}/**
 * Assuming XYZ is relative to D50, convert to CIE Lab
 * from CIE standard, which now defines these as a rational fraction
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function pi(t){const e=Ba(t[0]/Jn[0]),n=Ba(t[1]/Jn[1]);return[116*n-16,500*(e-n),200*(n-Ba(t[2]/Jn[2]))]}const Mu=216/24389,Du=24389/27;function Ba(t){return t>Mu?Math.cbrt(t):(Du*t+16)/116}const Bu=[.819022437996703,.3619062600528904,-.1288737815209879,.0329836539323885,.9292868615863434,.0361446663506424,.0481771893596242,.2642395317527308,.6335478284694309],Ru=[.210454268309314,.7936177747023054,-.0040720430116193,1.9779985324311684,-2.42859224204858,.450593709617411,.0259040424655478,.7827717124575296,-.8086757549230774];/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * XYZ <-> LMS matrices recalculated for consistent reference white
 * @see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484
 */function Ra(t){const e=At(Bu,t);return At(Ru,[Math.cbrt(e[0]),Math.cbrt(e[1]),Math.cbrt(e[2])])}const Ou=[30757411/17917100,-6372589/17917100,-4539589/17917100,-.666684351832489,1.616481236634939,467509/29648200,792561/44930125,-1921689/44930125,.942103121235474];/**
 * Convert XYZ to linear-light rec2020
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const Wu=[446124/178915,-333277/357830,-72051/178915,-14852/17905,63121/35810,423/17905,11844/330415,-50337/660830,316169/330415];/**
 * Convert XYZ to linear-light P3
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Tu(t){return At(Wu,t)}const Lu=[1.3457868816471583,-.25557208737979464,-.05110186497554526,-.5446307051249019,1.5082477428451468,.02052744743642139,0,0,1.2119675456389452];/**
 * Convert D50 XYZ to linear-light prophoto-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */const Iu=[1829569/896150,-506331/896150,-308931/896150,-851781/878810,1648619/878810,36519/878810,16779/1248040,-147721/1248040,1266979/1248040];/**
 * Convert XYZ to linear-light a98-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const _u=[12831/3959,-329/214,-1974/3959,-851781/878810,1648619/878810,36519/878810,705/12673,-2585/12673,705/667];/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Lr(t){return At(_u,t)}/**
 * Convert an array of linear-light rec2020 RGB  in the range 0.0-1.0
 * to gamma corrected form ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const di=1.09929682680944,Hu=.018053968510807;function Oa(t){const e=t<0?-1:1,n=Math.abs(t);return n>Hu?e*(di*Math.pow(n,.45)-(di-1)):4.5*t}/**
 * Convert an array of linear-light sRGB values in the range 0.0-1.0 to gamma corrected form
 * Extended transfer function:
 *  For negative values, linear portion extends on reflection
 *  of axis, then uses reflected pow below that
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */function Ir(t){return[Wa(t[0]),Wa(t[1]),Wa(t[2])]}function Wa(t){const e=t<0?-1:1,n=Math.abs(t);return n>.0031308?e*(1.055*Math.pow(n,1/2.4)-.055):12.92*t}/**
 * Convert an array of linear-light display-p3 RGB in the range 0.0-1.0
 * to gamma corrected form
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Uu(t){return Ir(t)}/**
 * Convert an array of linear-light prophoto-rgb in the range 0.0-1.0
 * to gamma corrected form.
 * Transfer curve is gamma 1.8 with a small linear portion.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const zu=1/512;function Ta(t){const e=t<0?-1:1,n=Math.abs(t);return n>=zu?e*Math.pow(n,1/1.8):16*t}/**
 * Convert an array of linear-light a98-rgb in the range 0.0-1.0
 * to gamma corrected form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function La(t){const e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,256/563)}/**
 * Convert an array of rec2020 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 * ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const mi=1.09929682680944,Gu=.018053968510807;function Ia(t){const e=t<0?-1:1,n=Math.abs(t);return n<4.5*Gu?t/4.5:e*Math.pow((n+mi-1)/mi,1/.45)}const ju=[63426534/99577255,20160776/139408157,47086771/278816314,26158966/99577255,.677998071518871,8267143/139408157,0,19567812/697040785,1.0609850577107909];/**
 * Convert an array of linear-light rec2020 values to CIE XYZ
 * using  D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 *//**
 * Convert an array of of sRGB values where in-gamut values are in the range
 * [0 - 1] to linear light (un-companded) form.
 * Extended transfer function:
 *  For negative values, linear portion is extended on reflection of axis,
 *  then reflected power function is used.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */function Ps(t){return[_a(t[0]),_a(t[1]),_a(t[2])]}function _a(t){const e=t<0?-1:1,n=Math.abs(t);return n<=.04045?t/12.92:e*Math.pow((n+.055)/1.055,2.4)}/**
 * Convert an array of display-p3 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function qu(t){return Ps(t)}const Vu=[608311/1250200,189793/714400,198249/1000160,35783/156275,247089/357200,198249/2500400,0,32229/714400,5220557/5000800];/**
 * Convert an array of linear-light display-p3 values to CIE XYZ
 * using D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */function Ku(t){return At(Vu,t)}/**
 * Convert an array of prophoto-rgb values where in-gamut Colors are in the
 * range [0.0 - 1.0] to linear light (un-companded) form. Transfer curve is
 * gamma 1.8 with a small linear portion. Extended transfer function
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const Xu=16/512;function Ha(t){const e=t<0?-1:1,n=Math.abs(t);return n<=Xu?t/16:e*Math.pow(n,1.8)}const Zu=[.7977666449006423,.13518129740053308,.0313477341283922,.2880748288194013,.711835234241873,8993693872564e-17,0,0,.8251046025104602];/**
 * Convert an array of linear-light prophoto-rgb values to CIE D50 XYZ.
 * Matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see see https://github.com/w3c/csswg-drafts/issues/7675
 */function Ua(t){const e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,563/256)}const Yu=[573536/994567,263643/1420810,187206/994567,591459/1989134,6239551/9945670,374412/4972835,53769/1989134,351524/4972835,4929758/4972835];/**
 * Convert an array of linear-light a98-rgb values to CIE XYZ
 * http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * has greater numerical precision than section ******* of
 * https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * but the values below were calculated from first principles
 * from the chromaticity coordinates of R G B W
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * @see https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/matrixmaker.html
 */const Qu=[506752/1228815,87881/245763,12673/70218,87098/409605,175762/245763,12673/175545,7918/409605,87881/737289,1001167/1053270];/**
 * Convert an array of linear-light sRGB values to CIE XYZ
 * using sRGB's own white, D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function _r(t){return At(Qu,t)}/**
 * Convert an array of gamma-corrected sRGB values in the 0.0 to 1.0 range to HSL.
 *
 * @param {Color} RGB [r, g, b]
 * - Red component 0..1
 * - Green component 0..1
 * - Blue component 0..1
 * @return {number[]} Array of HSL values: Hue as degrees 0..360, Saturation and Lightness as percentages 0..100
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/utilities.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/better-rgbToHsl.js
 */function Ju(t){const e=t[0],n=t[1],r=t[2],s=Math.max(e,n,r),a=Math.min(e,n,r),o=(a+s)/2,i=s-a;let l=Number.NaN,c=0;if(Math.round(1e5*i)!==0){const h=Math.round(1e5*o);switch(c=h===0||h===1e5?0:(s-o)/Math.min(o,1-o),s){case e:l=(n-r)/i+(n<r?6:0);break;case n:l=(r-e)/i+2;break;case r:l=(e-n)/i+4}l*=60}return c<0&&(l+=180,c=Math.abs(c)),l>=360&&(l-=360),[l,100*c,100*o]}function th(t){const e=t[0],n=t[1],r=t[2],s=Math.max(e,n,r),a=Math.min(e,n,r);let o=Number.NaN;const i=s-a;if(i!==0){switch(s){case e:o=(n-r)/i+(n<r?6:0);break;case n:o=(r-e)/i+2;break;case r:o=(e-n)/i+4}o*=60}return o>=360&&(o-=360),o}function eh(t){let e=t;return e=Ps(e),e=_r(e),e=He(e),e}function za(t){let e=t;return e=_e(e),e=Lr(e),e=Ir(e),e}function nh(t){let e=t;return e=ci(e),e=Ps(e),e=_r(e),e=He(e),e}function rh(t){let e=t;return e=_e(e),e=Lr(e),e=Ir(e),e=Ju(e),e}function sh(t){let e=t;return e=Fu(e),e=Ps(e),e=_r(e),e=He(e),e}function ah(t){let e=t;e=_e(e),e=Lr(e);const n=Ir(e),r=Math.min(n[0],n[1],n[2]),s=1-Math.max(n[0],n[1],n[2]);return[th(n),100*r,100*s]}function oh(t){let e=t;return e=ui(e),e}function ih(t){let e=t;return e=pi(e),e}function lh(t){let e=t;return e=xu(e),e=ui(e),e}function ch(t){let e=t;return e=pi(e),e=Su(e),e}function uh(t){let e=t;return e=Da(e),e=He(e),e}function hh(t){let e=t;return e=_e(e),e=Ra(e),e}function fh(t){let e=t;return e=hi(e),e=Da(e),e=He(e),e}function gi(t){let e=t;return e=_e(e),e=Ra(e),e=fi(e),e}function ph(t){let e=t;return e=_r(e),e=He(e),e}function dh(t){let e=t;return e=_e(e),e=Lr(e),e}function mh(t){let e=t;/**
 * Convert an array of a98-rgb values in the range 0.0 - 1.0
 * to linear light (un-companded) form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */var n;return e=[Ua((n=e)[0]),Ua(n[1]),Ua(n[2])],e=At(Yu,e),e=He(e),e}function gh(t){let e=t;var n;return e=_e(e),e=At(Iu,e),e=[La((n=e)[0]),La(n[1]),La(n[2])],e}function vh(t){let e=t;return e=qu(e),e=Ku(e),e=He(e),e}function bh(t){let e=t;return e=_e(e),e=Tu(e),e=Uu(e),e}function wh(t){let e=t;var n;return e=[Ia((n=e)[0]),Ia(n[1]),Ia(n[2])],e=At(ju,e),e=He(e),e}function $h(t){let e=t;var n;return e=_e(e),e=At(Ou,e),e=[Oa((n=e)[0]),Oa(n[1]),Oa(n[2])],e}function yh(t){let e=t;var n;return e=[Ha((n=e)[0]),Ha(n[1]),Ha(n[2])],e=At(Zu,e),e}function Nh(t){let e=t;var n;return e=At(Lu,e),e=[Ta((n=e)[0]),Ta(n[1]),Ta(n[2])],e}function Eh(t){let e=t;return e=He(e),e}function Ch(t){let e=t;return e=_e(e),e}function kh(t){return t[0]>=-1e-4&&t[0]<=1.0001&&t[1]>=-1e-4&&t[1]<=1.0001&&t[2]>=-1e-4&&t[2]<=1.0001}function vi(t){return[t[0]<0?0:t[0]>1?1:t[0],t[1]<0?0:t[1]>1?1:t[1],t[2]<0?0:t[2]>1?1:t[2]]}/**
 * @license MIT https://github.com/facelessuser/coloraide/blob/main/LICENSE.md
 */function Fh(t,e,n){const r=t[0],s=t[2];let a=e(t);const o=e([r,0,s]);for(let i=0;i<4;i++){if(i>0){const c=n(a);c[0]=r,c[2]=s,a=e(c)}const l=xh(o,a);if(!l)break;a=l}return vi(a)}function xh(t,e){let n=1/0,r=-1/0;const s=[0,0,0];for(let a=0;a<3;a++){const o=t[a],i=e[a]-o;s[a]=i;const l=0,c=1;if(i){const h=1/i,f=(l-o)*h,d=(c-o)*h;r=Math.max(Math.min(f,d),r),n=Math.min(Math.max(f,d),n)}else if(o<l||o>c)return!1}return!(r>n||n<0)&&(r<0&&(r=n),!!isFinite(r)&&[t[0]+s[0]*r,t[1]+s[1]*r,t[2]+s[2]*r])}const Sh={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};function bi(t){const[e,n,r]=t.map(s=>s<=.03928?s/12.92:Math.pow((s+.055)/1.055,2.4));return .2126*e+.7152*n+.0722*r}function wi(t,e){const n=bi(t),r=bi(e);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}var $,D;function Pt(t){return[Number.isNaN(t[0])?0:t[0],Number.isNaN(t[1])?0:t[1],Number.isNaN(t[2])?0:t[2]]}function $i(t){switch(t.colorNotation){case $.HEX:case $.RGB:case $.sRGB:return{...t,colorNotation:$.XYZ_D50,channels:eh(Pt(t.channels))};case $.Linear_sRGB:return{...t,colorNotation:$.XYZ_D50,channels:ph(Pt(t.channels))};case $.Display_P3:return{...t,colorNotation:$.XYZ_D50,channels:vh(Pt(t.channels))};case $.Rec2020:return{...t,colorNotation:$.XYZ_D50,channels:wh(Pt(t.channels))};case $.A98_RGB:return{...t,colorNotation:$.XYZ_D50,channels:mh(Pt(t.channels))};case $.ProPhoto_RGB:return{...t,colorNotation:$.XYZ_D50,channels:yh(Pt(t.channels))};case $.HSL:return{...t,colorNotation:$.XYZ_D50,channels:nh(Pt(t.channels))};case $.HWB:return{...t,colorNotation:$.XYZ_D50,channels:sh(Pt(t.channels))};case $.Lab:return{...t,colorNotation:$.XYZ_D50,channels:oh(Pt(t.channels))};case $.OKLab:return{...t,colorNotation:$.XYZ_D50,channels:uh(Pt(t.channels))};case $.LCH:return{...t,colorNotation:$.XYZ_D50,channels:lh(Pt(t.channels))};case $.OKLCH:return{...t,colorNotation:$.XYZ_D50,channels:fh(Pt(t.channels))};case $.XYZ_D50:return{...t,colorNotation:$.XYZ_D50,channels:Pt(t.channels)};case $.XYZ_D65:return{...t,colorNotation:$.XYZ_D50,channels:Eh(Pt(t.channels))};default:throw new Error("Unsupported color notation")}}(function(t){t.A98_RGB="a98-rgb",t.Display_P3="display-p3",t.HEX="hex",t.HSL="hsl",t.HWB="hwb",t.LCH="lch",t.Lab="lab",t.Linear_sRGB="srgb-linear",t.OKLCH="oklch",t.OKLab="oklab",t.ProPhoto_RGB="prophoto-rgb",t.RGB="rgb",t.sRGB="srgb",t.Rec2020="rec2020",t.XYZ_D50="xyz-d50",t.XYZ_D65="xyz-d65"})($||($={})),function(t){t.ColorKeyword="color-keyword",t.HasAlpha="has-alpha",t.HasDimensionValues="has-dimension-values",t.HasNoneKeywords="has-none-keywords",t.HasNumberValues="has-number-values",t.HasPercentageAlpha="has-percentage-alpha",t.HasPercentageValues="has-percentage-values",t.HasVariableAlpha="has-variable-alpha",t.Hex="hex",t.LegacyHSL="legacy-hsl",t.LegacyRGB="legacy-rgb",t.NamedColor="named-color",t.RelativeColorSyntax="relative-color-syntax",t.ColorMix="color-mix",t.ContrastColor="contrast-color",t.Experimental="experimental"}(D||(D={}));const yi=new Set([$.A98_RGB,$.Display_P3,$.HEX,$.Linear_sRGB,$.ProPhoto_RGB,$.RGB,$.sRGB,$.Rec2020,$.XYZ_D50,$.XYZ_D65]);function tr(t,e){const n={...t};if(t.colorNotation!==e){const r=$i(n);switch(e){case $.HEX:case $.RGB:n.colorNotation=$.RGB,n.channels=za(r.channels);break;case $.sRGB:n.colorNotation=$.sRGB,n.channels=za(r.channels);break;case $.Linear_sRGB:n.colorNotation=$.Linear_sRGB,n.channels=dh(r.channels);break;case $.Display_P3:n.colorNotation=$.Display_P3,n.channels=bh(r.channels);break;case $.Rec2020:n.colorNotation=$.Rec2020,n.channels=$h(r.channels);break;case $.ProPhoto_RGB:n.colorNotation=$.ProPhoto_RGB,n.channels=Nh(r.channels);break;case $.A98_RGB:n.colorNotation=$.A98_RGB,n.channels=gh(r.channels);break;case $.HSL:n.colorNotation=$.HSL,n.channels=rh(r.channels);break;case $.HWB:n.colorNotation=$.HWB,n.channels=ah(r.channels);break;case $.Lab:n.colorNotation=$.Lab,n.channels=ih(r.channels);break;case $.LCH:n.colorNotation=$.LCH,n.channels=ch(r.channels);break;case $.OKLCH:n.colorNotation=$.OKLCH,n.channels=gi(r.channels);break;case $.OKLab:n.colorNotation=$.OKLab,n.channels=hh(r.channels);break;case $.XYZ_D50:n.colorNotation=$.XYZ_D50,n.channels=r.channels;break;case $.XYZ_D65:n.colorNotation=$.XYZ_D65,n.channels=Ch(r.channels);break;default:throw new Error("Unsupported color notation")}}else n.channels=Pt(t.channels);if(e===t.colorNotation)n.channels=Vt(t.channels,[0,1,2],n.channels,[0,1,2]);else if(yi.has(e)&&yi.has(t.colorNotation))n.channels=Vt(t.channels,[0,1,2],n.channels,[0,1,2]);else switch(e){case $.HSL:switch(t.colorNotation){case $.HWB:n.channels=Vt(t.channels,[0],n.channels,[0]);break;case $.Lab:case $.OKLab:n.channels=Vt(t.channels,[2],n.channels,[0]);break;case $.LCH:case $.OKLCH:n.channels=Vt(t.channels,[0,1,2],n.channels,[2,1,0])}break;case $.HWB:switch(t.colorNotation){case $.HSL:n.channels=Vt(t.channels,[0],n.channels,[0]);break;case $.LCH:case $.OKLCH:n.channels=Vt(t.channels,[0],n.channels,[2])}break;case $.Lab:case $.OKLab:switch(t.colorNotation){case $.HSL:n.channels=Vt(t.channels,[0],n.channels,[2]);break;case $.Lab:case $.OKLab:n.channels=Vt(t.channels,[0,1,2],n.channels,[0,1,2]);break;case $.LCH:case $.OKLCH:n.channels=Vt(t.channels,[0],n.channels,[0])}break;case $.LCH:case $.OKLCH:switch(t.colorNotation){case $.HSL:n.channels=Vt(t.channels,[0,1,2],n.channels,[2,1,0]);break;case $.HWB:n.channels=Vt(t.channels,[0],n.channels,[2]);break;case $.Lab:case $.OKLab:n.channels=Vt(t.channels,[0],n.channels,[0]);break;case $.LCH:case $.OKLCH:n.channels=Vt(t.channels,[0,1,2],n.channels,[0,1,2])}}return n.channels=Ah(n.channels,e),n}function Ah(t,e){const n=[...t];switch(e){case $.HSL:!Number.isNaN(n[1])&&Hr(n[1],4)<=0&&(n[0]=Number.NaN);break;case $.HWB:Math.max(0,Hr(n[1],4))+Math.max(0,Hr(n[2],4))>=100&&(n[0]=Number.NaN);break;case $.LCH:!Number.isNaN(n[1])&&Hr(n[1],4)<=0&&(n[2]=Number.NaN);break;case $.OKLCH:!Number.isNaN(n[1])&&Hr(n[1],6)<=0&&(n[2]=Number.NaN)}return n}function Vt(t,e,n,r){const s=[...n];for(const a of e)Number.isNaN(t[e[a]])&&(s[r[a]]=Number.NaN);return s}function Ni(t){const e=new Map;switch(t.colorNotation){case $.RGB:case $.HEX:e.set("r",et(255*t.channels[0])),e.set("g",et(255*t.channels[1])),e.set("b",et(255*t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case $.HSL:e.set("h",et(t.channels[0])),e.set("s",et(t.channels[1])),e.set("l",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case $.HWB:e.set("h",et(t.channels[0])),e.set("w",et(t.channels[1])),e.set("b",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case $.Lab:case $.OKLab:e.set("l",et(t.channels[0])),e.set("a",et(t.channels[1])),e.set("b",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case $.LCH:case $.OKLCH:e.set("l",et(t.channels[0])),e.set("c",et(t.channels[1])),e.set("h",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case $.sRGB:case $.A98_RGB:case $.Display_P3:case $.Rec2020:case $.Linear_sRGB:case $.ProPhoto_RGB:e.set("r",et(t.channels[0])),e.set("g",et(t.channels[1])),e.set("b",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha));break;case $.XYZ_D50:case $.XYZ_D65:e.set("x",et(t.channels[0])),e.set("y",et(t.channels[1])),e.set("z",et(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",et(t.alpha))}return e}function Ei(t){const e=new Map(t);for(const[n,r]of t)Number.isNaN(r[4].value)&&e.set(n,et(0));return e}function et(t){return Number.isNaN(t)?[v.Number,"none",-1,-1,{value:Number.NaN,type:x.Number}]:[v.Number,t.toString(),-1,-1,{value:t,type:x.Number}]}function Hr(t,e=7){if(Number.isNaN(t))return 0;const n=Math.pow(10,e);return Math.round(t*n)/n}function V(t,e,n,r){return Math.min(Math.max(t/e,n),r)}const Ph=/[A-Z]/g;function it(t){return t.replace(Ph,e=>String.fromCharCode(e.charCodeAt(0)+32))}function Ur(t,e,n){if(vt(t)&&it(t[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(tt(t)){e!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(t[4].value,100,-2147483647,2147483647);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(t[4].value,1,-2147483647,2147483647);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}const Mh=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]);function Dh(t,e){const n=[],r=[],s=[],a=[];let o,i,l=!1,c=!1;const h={colorNotation:$.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};let f=n;for(let b=0;b<t.value.length;b++){let w=t.value[b];if(ae(w)||oe(w))for(;ae(t.value[b+1])||oe(t.value[b+1]);)b++;else if(f===n&&n.length&&(f=r),f===r&&r.length&&(f=s),I(w)&&es(w.value)&&w.value[4].value==="/"){if(f===a)return!1;f=a}else{if(se(w)){if(f===a&&it(w.getName())==="var"){h.syntaxFlags.add(D.HasVariableAlpha),f.push(w);continue}if(!ls.has(it(w.getName())))return!1;const[[N]]=br([[w]],{censorIntoStandardRepresentableValues:!0,globals:i,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!N||!I(N)||!pt(N.value))return!1;Number.isNaN(N.value[4].value)&&(N.value[4].value=0),w=N}if(f===n&&n.length===0&&I(w)&&vt(w.value)&&Mh.has(it(w.value[4].value))){if(l)return!1;l=it(w.value[4].value),h.colorNotation=Bh(l),c&&(c.colorNotation!==h.colorNotation&&(c=tr(c,h.colorNotation)),o=Ni(c),i=Ei(o))}else if(f===n&&n.length===0&&I(w)&&vt(w.value)&&it(w.value[4].value)==="from"){if(c||l)return!1;for(;ae(t.value[b+1])||oe(t.value[b+1]);)b++;if(b++,w=t.value[b],c=e(w),c===!1)return!1;c.syntaxFlags.has(D.Experimental)&&h.syntaxFlags.add(D.Experimental),h.syntaxFlags.add(D.RelativeColorSyntax)}else{if(!I(w))return!1;if(vt(w.value)&&o&&o.has(it(w.value[4].value))){f.push(new G(o.get(it(w.value[4].value))));continue}f.push(w)}}}if(!l||f.length!==1||n.length!==1||r.length!==1||s.length!==1||!I(n[0])||!I(r[0])||!I(s[0])||o&&!o.has("alpha"))return!1;const d=Ur(n[0].value,0,h);if(!d||!H(d))return!1;const p=Ur(r[0].value,1,h);if(!p||!H(p))return!1;const m=Ur(s[0].value,2,h);if(!m||!H(m))return!1;const y=[d,p,m];if(a.length===1)if(h.syntaxFlags.add(D.HasAlpha),I(a[0])){const b=Ur(a[0].value,3,h);if(!b||!H(b))return!1;y.push(b)}else h.alpha=a[0];else if(o&&o.has("alpha")){const b=Ur(o.get("alpha"),3,h);if(!b||!H(b))return!1;y.push(b)}return h.channels=[y[0][4].value,y[1][4].value,y[2][4].value],y.length===4&&(h.alpha=y[3][4].value),h}function Bh(t){switch(t){case"srgb":return $.sRGB;case"srgb-linear":return $.Linear_sRGB;case"display-p3":return $.Display_P3;case"a98-rgb":return $.A98_RGB;case"prophoto-rgb":return $.ProPhoto_RGB;case"rec2020":return $.Rec2020;case"xyz":case"xyz-d65":return $.XYZ_D65;case"xyz-d50":return $.XYZ_D50;default:throw new Error("Unknown color space name: "+t)}}const Rh=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","lab","oklab","xyz","xyz-d50","xyz-d65"]),Ga=new Set(["hsl","hwb","lch","oklch"]),Oh=new Set(["shorter","longer","increasing","decreasing"]);function Wh(t,e){let n=null,r=null,s=null,a=!1;for(let o=0;o<t.value.length;o++){const i=t.value[o];if(!ae(i)&&!oe(i)){if(I(i)&&vt(i.value)){if(!n&&it(i.value[4].value)==="in"){n=i;continue}if(n&&!r){r=it(i.value[4].value);continue}if(n&&r&&!s&&Ga.has(r)){s=it(i.value[4].value);continue}if(n&&r&&s&&!a&&it(i.value[4].value)==="hue"){a=!0;continue}return!1}return!(!I(i)||!ne(i.value))&&!!r&&(s||a?!!(r&&s&&a&&Ga.has(r)&&Oh.has(s))&&Ci(r,s,ja(t.value.slice(o+1),e)):Rh.has(r)?Th(r,ja(t.value.slice(o+1),e)):!!Ga.has(r)&&Ci(r,"shorter",ja(t.value.slice(o+1),e)))}}return!1}function ja(t,e){const n=[];let r=1,s=!1,a=!1;for(let l=0;l<t.length;l++){let c=t[l];if(!ae(c)&&!oe(c)){if(!I(c)||!ne(c.value)){if(!s){const h=e(c);if(h){s=h;continue}}if(!a){if(se(c)&&ls.has(it(c.getName()))){if([[c]]=br([[c]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0}),!c||!I(c)||!pt(c.value))return!1;Number.isNaN(c.value[4].value)&&(c.value[4].value=0)}if(I(c)&&tt(c.value)&&c.value[4].value>=0){a=c.value[4].value;continue}}return!1}if(!s)return!1;n.push({color:s,percentage:a}),s=!1,a=!1}}if(s&&n.push({color:s,percentage:a}),n.length!==2)return!1;let o=n[0].percentage,i=n[1].percentage;return(o===!1||!(o<0||o>100))&&(i===!1||!(i<0||i>100))&&(o===!1&&i===!1?(o=50,i=50):o!==!1&&i===!1?i=100-o:o===!1&&i!==!1&&(o=100-i),(o!==0||i!==0)&&o!==!1&&i!==!1&&(o+i>100&&(o=o/(o+i)*100,i=i/(o+i)*100),o+i<100&&(r=(o+i)/100,o=o/(o+i)*100,i=i/(o+i)*100),{a:{color:n[0].color,percentage:o},b:{color:n[1].color,percentage:i},alphaMultiplier:r}))}function Th(t,e){if(!e)return!1;const n=e.a.color,r=e.b.color,s=e.a.percentage/100;let a=n.channels,o=r.channels,i=$.RGB,l=n.alpha;if(typeof l!="number")return!1;let c=r.alpha;if(typeof c!="number")return!1;switch(l=Number.isNaN(l)?c:l,c=Number.isNaN(c)?l:c,t){case"srgb":i=$.RGB;break;case"srgb-linear":i=$.Linear_sRGB;break;case"display-p3":i=$.Display_P3;break;case"a98-rgb":i=$.A98_RGB;break;case"prophoto-rgb":i=$.ProPhoto_RGB;break;case"rec2020":i=$.Rec2020;break;case"lab":i=$.Lab;break;case"oklab":i=$.OKLab;break;case"xyz-d50":i=$.XYZ_D50;break;case"xyz":case"xyz-d65":i=$.XYZ_D65}a=tr(n,i).channels,o=tr(r,i).channels,a[0]=ge(a[0],o[0]),o[0]=ge(o[0],a[0]),a[1]=ge(a[1],o[1]),o[1]=ge(o[1],a[1]),a[2]=ge(a[2],o[2]),o[2]=ge(o[2],a[2]),a[0]=Ue(a[0],l),a[1]=Ue(a[1],l),a[2]=Ue(a[2],l),o[0]=Ue(o[0],c),o[1]=Ue(o[1],c),o[2]=Ue(o[2],c);const h=Ne(l,c,s),f={colorNotation:i,channels:[kn(Ne(a[0],o[0],s),h),kn(Ne(a[1],o[1],s),h),kn(Ne(a[2],o[2],s),h)],alpha:h*e.alphaMultiplier,syntaxFlags:new Set([D.ColorMix])};return(e.a.color.syntaxFlags.has(D.Experimental)||e.b.color.syntaxFlags.has(D.Experimental))&&f.syntaxFlags.add(D.Experimental),f}function Ci(t,e,n){if(!n)return!1;const r=n.a.color,s=n.b.color,a=n.a.percentage/100;let o=r.channels,i=s.channels,l=0,c=0,h=0,f=0,d=0,p=0,m=$.RGB,y=r.alpha;if(typeof y!="number")return!1;let b=s.alpha;if(typeof b!="number")return!1;switch(y=Number.isNaN(y)?b:y,b=Number.isNaN(b)?y:b,t){case"hsl":m=$.HSL;break;case"hwb":m=$.HWB;break;case"lch":m=$.LCH;break;case"oklch":m=$.OKLCH}switch(o=tr(r,m).channels,i=tr(s,m).channels,t){case"hsl":case"hwb":l=o[0],c=i[0],h=o[1],f=i[1],d=o[2],p=i[2];break;case"lch":case"oklch":h=o[0],f=i[0],d=o[1],p=i[1],l=o[2],c=i[2]}l=ge(l,c),Number.isNaN(l)&&(l=0),c=ge(c,l),Number.isNaN(c)&&(c=0),h=ge(h,f),f=ge(f,h),d=ge(d,p),p=ge(p,d);const w=c-l;switch(e){case"shorter":w>180?l+=360:w<-180&&(c+=360);break;case"longer":-180<w&&w<180&&(w>0?l+=360:c+=360);break;case"increasing":w<0&&(c+=360);break;case"decreasing":w>0&&(l+=360);break;default:throw new Error("Unknown hue interpolation method")}h=Ue(h,y),d=Ue(d,y),f=Ue(f,b),p=Ue(p,b);let N=[0,0,0];const E=Ne(y,b,a);switch(t){case"hsl":case"hwb":N=[Ne(l,c,a),kn(Ne(h,f,a),E),kn(Ne(d,p,a),E)];break;case"lch":case"oklch":N=[kn(Ne(h,f,a),E),kn(Ne(d,p,a),E),Ne(l,c,a)]}const M={colorNotation:m,channels:N,alpha:E*n.alphaMultiplier,syntaxFlags:new Set([D.ColorMix])};return(n.a.color.syntaxFlags.has(D.Experimental)||n.b.color.syntaxFlags.has(D.Experimental))&&M.syntaxFlags.add(D.Experimental),M}function ge(t,e){return Number.isNaN(t)?e:t}function Ne(t,e,n){return t*n+e*(1-n)}function Ue(t,e){return Number.isNaN(e)?t:Number.isNaN(t)?Number.NaN:t*e}function kn(t,e){return e===0||Number.isNaN(e)?t:Number.isNaN(t)?Number.NaN:t/e}function Lh(t){const e=it(t[4].value);if(e.match(/[^a-f0-9]/))return!1;const n={colorNotation:$.HEX,channels:[0,0,0],alpha:1,syntaxFlags:new Set([D.Hex])},r=e.length;if(r===3){const s=e[0],a=e[1],o=e[2];return n.channels=[parseInt(s+s,16)/255,parseInt(a+a,16)/255,parseInt(o+o,16)/255],n}if(r===6){const s=e[0]+e[1],a=e[2]+e[3],o=e[4]+e[5];return n.channels=[parseInt(s,16)/255,parseInt(a,16)/255,parseInt(o,16)/255],n}if(r===4){const s=e[0],a=e[1],o=e[2],i=e[3];return n.channels=[parseInt(s+s,16)/255,parseInt(a+a,16)/255,parseInt(o+o,16)/255],n.alpha=parseInt(i+i,16)/255,n.syntaxFlags.add(D.HasAlpha),n}if(r===8){const s=e[0]+e[1],a=e[2]+e[3],o=e[4]+e[5],i=e[6]+e[7];return n.channels=[parseInt(s,16)/255,parseInt(a,16)/255,parseInt(o,16)/255],n.alpha=parseInt(i,16)/255,n.syntaxFlags.add(D.HasAlpha),n}return!1}function zr(t){if(H(t))return t[4].value=t[4].value%360,t[1]=t[4].value.toString(),t;if(nt(t)){let e=t[4].value;switch(it(t[4].unit)){case"deg":break;case"rad":e=180*t[4].value/Math.PI;break;case"grad":e=.9*t[4].value;break;case"turn":e=360*t[4].value;break;default:return!1}return e%=360,[v.Number,e.toString(),t[2],t[3],{value:e,type:x.Number}]}return!1}function Ih(t,e,n){if(e===0){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(tt(t)){e===3?n.syntaxFlags.add(D.HasPercentageAlpha):n.syntaxFlags.add(D.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){if(e!==3)return!1;let r=V(t[4].value,1,0,100);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function _h(t,e,n){if(vt(t)&&it(t[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(e===0){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(tt(t)){e===3?n.syntaxFlags.add(D.HasPercentageAlpha):n.syntaxFlags.add(D.HasPercentageValues);let r=t[4].value;return e===3?r=V(t[4].value,100,0,1):e===1&&(r=V(t[4].value,1,0,2147483647)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=t[4].value;return e===3?r=V(t[4].value,1,0,1):e===1&&(r=V(t[4].value,1,0,2147483647)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function ki(t,e,n,r){const s=[],a=[],o=[],i=[],l={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let c=s;for(let m=0;m<t.value.length;m++){let y=t.value[m];if(!ae(y)&&!oe(y)){if(I(y)&&ne(y.value)){if(c===s){c=a;continue}if(c===a){c=o;continue}if(c===o){c=i;continue}if(c===i)return!1}if(se(y)){if(c===i&&y.getName().toLowerCase()==="var"){l.syntaxFlags.add(D.HasVariableAlpha),c.push(y);continue}if(!ls.has(y.getName().toLowerCase()))return!1;const[[b]]=br([[y]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!b||!I(b)||!pt(b.value))return!1;Number.isNaN(b.value[4].value)&&(b.value[4].value=0),y=b}if(!I(y))return!1;c.push(y)}}if(c.length!==1||s.length!==1||a.length!==1||o.length!==1||!I(s[0])||!I(a[0])||!I(o[0]))return!1;const h=e(s[0].value,0,l);if(!h||!H(h))return!1;const f=e(a[0].value,1,l);if(!f||!H(f))return!1;const d=e(o[0].value,2,l);if(!d||!H(d))return!1;const p=[h,f,d];if(i.length===1)if(l.syntaxFlags.add(D.HasAlpha),I(i[0])){const m=e(i[0].value,3,l);if(!m||!H(m))return!1;p.push(m)}else l.alpha=i[0];return l.channels=[p[0][4].value,p[1][4].value,p[2][4].value],p.length===4&&(l.alpha=p[3][4].value),l}function Fn(t,e,n,r,s){const a=[],o=[],i=[],l=[];let c,h,f=!1;const d={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let p=a;for(let N=0;N<t.value.length;N++){let E=t.value[N];if(ae(E)||oe(E))for(;ae(t.value[N+1])||oe(t.value[N+1]);)N++;else if(p===a&&a.length&&(p=o),p===o&&o.length&&(p=i),I(E)&&es(E.value)&&E.value[4].value==="/"){if(p===l)return!1;p=l}else{if(se(E)){if(p===l&&E.getName().toLowerCase()==="var"){d.syntaxFlags.add(D.HasVariableAlpha),p.push(E);continue}if(!ls.has(E.getName().toLowerCase()))return!1;const[[M]]=br([[E]],{censorIntoStandardRepresentableValues:!0,globals:h,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!M||!I(M)||!pt(M.value))return!1;Number.isNaN(M.value[4].value)&&(M.value[4].value=0),E=M}if(p===a&&a.length===0&&I(E)&&vt(E.value)&&E.value[4].value.toLowerCase()==="from"){if(f)return!1;for(;ae(t.value[N+1])||oe(t.value[N+1]);)N++;if(N++,E=t.value[N],f=s(E),f===!1)return!1;f.syntaxFlags.has(D.Experimental)&&d.syntaxFlags.add(D.Experimental),d.syntaxFlags.add(D.RelativeColorSyntax),f.colorNotation!==n&&(f=tr(f,n)),c=Ni(f),h=Ei(c)}else{if(!I(E))return!1;if(vt(E.value)&&c){const M=E.value[4].value.toLowerCase();if(c.has(M)){p.push(new G(c.get(M)));continue}}p.push(E)}}}if(p.length!==1||a.length!==1||o.length!==1||i.length!==1||!I(a[0])||!I(o[0])||!I(i[0])||c&&!c.has("alpha"))return!1;const m=e(a[0].value,0,d);if(!m||!H(m))return!1;const y=e(o[0].value,1,d);if(!y||!H(y))return!1;const b=e(i[0].value,2,d);if(!b||!H(b))return!1;const w=[m,y,b];if(l.length===1)if(d.syntaxFlags.add(D.HasAlpha),I(l[0])){const N=e(l[0].value,3,d);if(!N||!H(N))return!1;w.push(N)}else d.alpha=l[0];else if(c&&c.has("alpha")){const N=e(c.get("alpha"),3,d);if(!N||!H(N))return!1;w.push(N)}return d.channels=[w[0][4].value,w[1][4].value,w[2][4].value],w.length===4&&(d.alpha=w[3][4].value),d}function Hh(t,e){if(t.value.some(n=>I(n)&&ne(n.value))){const n=Uh(t);if(n!==!1)return n}{const n=zh(t,e);if(n!==!1)return n}return!1}function Uh(t){return ki(t,Ih,$.HSL,[D.LegacyHSL])}function zh(t,e){return Fn(t,_h,$.HSL,[],e)}function Gh(t,e,n){if(vt(t)&&it(t[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(e===0){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(tt(t)){e===3?n.syntaxFlags.add(D.HasPercentageAlpha):n.syntaxFlags.add(D.HasPercentageValues);let r=t[4].value;return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=t[4].value;return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function jh(t,e,n){if(vt(t)&&it(t[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(tt(t)){e!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===1||e===2?r=V(t[4].value,.8,-2147483647,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(t[4].value,1,0,100);return e===1||e===2?r=V(t[4].value,1,-2147483647,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function qh(t,e){return Fn(t,jh,$.Lab,[],e)}function Vh(t,e,n){if(vt(t)&&it(t[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(e===2){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(tt(t)){e!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===1?r=V(t[4].value,100/150,0,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(t[4].value,1,0,100);return e===1?r=V(t[4].value,1,0,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function Kh(t,e){return Fn(t,Vh,$.LCH,[],e)}const Fi=new Map;for(const[t,e]of Object.entries(Sh))Fi.set(t,e);function Xh(t){const e=Fi.get(it(t));return!!e&&{colorNotation:$.RGB,channels:[e[0]/255,e[1]/255,e[2]/255],alpha:1,syntaxFlags:new Set([D.ColorKeyword,D.NamedColor])}}function Zh(t,e,n){if(vt(t)&&it(t[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(tt(t)){e!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(t[4].value,100,0,1);return e===1||e===2?r=V(t[4].value,250,-2147483647,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(t[4].value,1,0,1);return e===1||e===2?r=V(t[4].value,1,-2147483647,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function Yh(t,e){return Fn(t,Zh,$.OKLab,[],e)}function Qh(t,e,n){if(vt(t)&&it(t[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(e===2){const r=zr(t);return r!==!1&&(nt(t)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(tt(t)){e!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(t[4].value,100,0,1);return e===1?r=V(t[4].value,250,0,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(t[4].value,1,0,1);return e===1?r=V(t[4].value,1,0,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function Jh(t,e){return Fn(t,Qh,$.OKLCH,[],e)}function t0(t,e,n){if(tt(t)){e===3?n.syntaxFlags.add(D.HasPercentageAlpha):n.syntaxFlags.add(D.HasPercentageValues);const r=V(t[4].value,100,0,1);return[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(t[4].value,255,0,1);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function e0(t,e,n){if(vt(t)&&t[4].value.toLowerCase()==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:x.Number}];if(tt(t)){e!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(t[4].value,100,-2147483647,2147483647);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(t[4].value,255,-2147483647,2147483647);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:x.Number}]}return!1}function n0(t,e){if(t.value.some(n=>I(n)&&ne(n.value))){const n=r0(t);if(n!==!1)return(!n.syntaxFlags.has(D.HasNumberValues)||!n.syntaxFlags.has(D.HasPercentageValues))&&n}else{const n=s0(t,e);if(n!==!1)return n}return!1}function r0(t){return ki(t,t0,$.RGB,[D.LegacyRGB])}function s0(t,e){return Fn(t,e0,$.RGB,[],e)}function a0(t){const e=za(t);if(kh(e))return vi(e);let n=t;return n=gi(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),Ir(Fh(n,o0,i0))}function o0(t){return t=hi(t),t=Da(t),Lr(t)}function i0(t){return t=_r(t),t=Ra(t),fi(t)}function l0(t,e){let n=!1,r=!1;for(let i=0;i<t.value.length;i++){const l=t.value[i];if(!ae(l)&&!oe(l)&&(n||(n=e(l),!n))){if(!n||r||!I(l)||!vt(l.value)||it(l.value[4].value)!=="max")return!1;r=!0}}if(!n||!r)return!1;n.channels=Pt(n.channels),n.channels=a0($i(n).channels),n.colorNotation=$.sRGB;const s={colorNotation:$.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([D.ContrastColor,D.Experimental])},a=wi(n.channels,[1,1,1]),o=wi(n.channels,[0,0,0]);return s.channels=a>o?[1,1,1]:[0,0,0],s}function Ee(t){if(se(t))switch(it(t.getName())){case"rgb":case"rgba":return n0(t,Ee);case"hsl":case"hsla":return Hh(t,Ee);case"hwb":return e=Ee,Fn(t,Gh,$.HWB,[],e);case"lab":return qh(t,Ee);case"lch":return Kh(t,Ee);case"oklab":return Yh(t,Ee);case"oklch":return Jh(t,Ee);case"color":return Dh(t,Ee);case"color-mix":return Wh(t,Ee);case"contrast-color":return l0(t,Ee)}var e;if(I(t)){if(Al(t.value))return Lh(t.value);if(vt(t.value)){const n=Xh(t.value[4].value);return n!==!1?n:it(t.value[4].value)==="transparent"&&{colorNotation:$.RGB,channels:[0,0,0],alpha:0,syntaxFlags:new Set([D.ColorKeyword])}}}return!1}const{CloseParen:xi,Comment:Si,Dimension:c0,EOF:Ai,Function:Pi,Ident:u0,Number:h0,OpenParen:Mi,Percentage:f0,Whitespace:Di}=v,{HasNoneKeywords:qa}=D,Bi="relative-color",p0=8,er=10,Va=16,d0=100,Ka=255,Ri=new RegExp(`^${xr}(${jn}|${ws})\\s+`),m0=/(?:hsla?|hwb)$/,g0=new RegExp(`^(?:${Wo}|${Jc})$`),v0=new RegExp(Ao),b0=new RegExp(xr),Oi=new RegExp(`^${eu}`),Wi=new RegExp(`^${xr}`),w0=new RegExp(kr);function Ti(t,e={}){if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{colorSpace:n="",format:r=""}=e,s=new Map([["color",["r","g","b","alpha"]],["hsl",["h","s","l","alpha"]],["hsla",["h","s","l","alpha"]],["hwb",["h","w","b","alpha"]],["lab",["l","a","b","alpha"]],["lch",["l","c","h","alpha"]],["oklab",["l","a","b","alpha"]],["oklch",["l","c","h","alpha"]],["rgb",["r","g","b","alpha"]],["rgba",["r","g","b","alpha"]]]).get(n);if(!s)return new P;const a=new Set,o=[[],[],[],[]];let i=0,l=0,c=!1;for(;t.length;){const f=t.shift();if(!Array.isArray(f))throw new TypeError(`${f} is not an array.`);const[d,p,,,m]=f,y=o[i];if(Array.isArray(y))switch(d){case c0:{const b=Ki(f,e);S(b)?y.push(b):y.push(p);break}case Pi:{y.push(p),c=!0,l++,v0.test(p)&&a.add(l);break}case u0:{if(!s.includes(p))return new P;y.push(p),c||i++;break}case h0:{y.push(Number(m?.value)),c||i++;break}case Mi:{y.push(p),l++;break}case xi:{c&&(y[y.length-1]===" "?y.splice(-1,1,p):y.push(p),a.has(l)&&a.delete(l),l--,l===0&&(c=!1,i++));break}case f0:{y.push(Number(m?.value)/d0),c||i++;break}case Di:{if(y.length&&c){const b=y[y.length-1];(typeof b=="number"||S(b)&&!b.endsWith("(")&&b!==" ")&&y.push(p)}break}default:d!==Si&&d!==Ai&&c&&y.push(p)}}const h=[];for(const f of o)if(f.length===1){const[d]=f;Un(d)&&h.push(d)}else if(f.length){const d=X0(f.join(""),{format:r});h.push(d)}return h}function $0(t,e={}){const{currentColor:n="",format:r=""}=e;if(S(t)){if(t=t.toLowerCase().trim(),!t)return new P;if(!Wi.test(t))return t}else return new P;const s=gt({namespace:Bi,name:"extractOriginColor",value:t},e),a=mt(s);if(a instanceof st)return a.isNull?a:a.item;if(/currentcolor/.test(t))if(n)t=t.replace(/currentcolor/g,n);else return F(s,null),new P;let o="";if(Oi.test(t)&&([,o]=t.match(Oi)),e.colorSpace=o,Ri.test(t)){const[,i]=t.match(Ri),[,l]=t.split(i);if(/^[a-z]+$/.test(i)){if(!/^transparent$/.test(i)&&!Object.prototype.hasOwnProperty.call(Yn,i))return F(s,null),new P}else if(r===rt){const c=nr(i,e);S(c)&&(t=t.replace(i,c))}if(r===rt){const c=Ve({css:l}),h=Ti(c,e);if(h instanceof P)return F(s,null),h;const[f,d,p,m]=h;let y="";Un(m)?y=` ${f} ${d} ${p} / ${m})`:y=` ${h.join(" ")})`,l!==y&&(t=t.replace(l,y))}}else{const[,i]=t.split(Wi),l=Ve({css:i}),c=[];let h=0;for(;l.length;){const[N,E]=l.shift();switch(N){case Pi:case Mi:{c.push(E),h++;break}case xi:{const M=c[c.length-1];M===" "?c.splice(-1,1,E):S(M)&&c.push(E),h--;break}case Di:{const M=c[c.length-1];S(M)&&!M.endsWith("(")&&M!==" "&&c.push(E);break}default:N!==Si&&N!==Ai&&c.push(E)}if(h===0)break}const f=Xa(c.join("").trim(),e);if(f instanceof P)return F(s,null),f;const d=Ti(l,e);if(d instanceof P)return F(s,null),d;const[p,m,y,b]=d;let w="";Un(b)?w=` ${p} ${m} ${y} / ${b})`:w=` ${d.join(" ")})`,t=t.replace(i,`${f}${w}`)}return F(s,t),t}function Xa(t,e={}){const{format:n=""}=e;if(S(t)){if(w0.test(t)){if(n===rt)return t;throw new SyntaxError(`Unexpected token ${oa} found.`)}else if(!b0.test(t))return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=gt({namespace:Bi,name:"resolveRelativeColor",value:t},e),s=mt(r);if(s instanceof st)return s.isNull?s:s.item;const a=$0(t,e);if(a instanceof P)return F(r,null),a;if(t=a,n===rt)return t.startsWith("rgba(")?t=t.replace(/^rgba\(/,"rgb("):t.startsWith("hsla(")&&(t=t.replace(/^hsla\(/,"hsl(")),t;const o=Ve({css:t}),i=Wl(o),l=Ee(i);if(!l)return F(r,null),new P;const{alpha:c,channels:h,colorNotation:f,syntaxFlags:d}=l;let p;Number.isNaN(Number(c))?d instanceof Set&&d.has(qa)?p=g:p=0:p=B(Number(c),p0);let m,y,b;[m,y,b]=h;let w;if(g0.test(f)){const N=d instanceof Set&&d.has(qa);Number.isNaN(m)?N?m=g:m=0:m=B(m,Va),Number.isNaN(y)?N?y=g:y=0:y=B(y,Va),Number.isNaN(b)?N?b=g:b=0:b=B(b,Va),p===1?w=`${f}(${m} ${y} ${b})`:w=`${f}(${m} ${y} ${b} / ${p})`}else if(m0.test(f)){Number.isNaN(m)&&(m=0),Number.isNaN(y)&&(y=0),Number.isNaN(b)&&(b=0);let[N,E,M]=Wr(`${f}(${m} ${y} ${b} / ${p})`);N=B(N/Ka,er),E=B(E/Ka,er),M=B(M/Ka,er),p===1?w=`color(srgb ${N} ${E} ${M})`:w=`color(srgb ${N} ${E} ${M} / ${p})`}else{const N=f==="rgb"?"srgb":f,E=d instanceof Set&&d.has(qa);Number.isNaN(m)?E?m=g:m=0:m=B(m,er),Number.isNaN(y)?E?y=g:y=0:y=B(y,er),Number.isNaN(b)?E?b=g:b=0:b=B(b,er),p===1?w=`color(${N} ${m} ${y} ${b})`:w=`color(${N} ${m} ${y} ${b} / ${p})`}return F(r,w),w}const y0="resolve",Gr="rgba(0, 0, 0, 0)",N0=new RegExp(gs),E0=new RegExp(Io),C0=new RegExp(kr),nr=(t,e={})=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{currentColor:n="",format:r=ot,nullable:s=!1}=e,a=gt({namespace:y0,name:"resolve",value:t},e),o=mt(a);if(o instanceof st)return o.isNull?o:o.item;if(C0.test(t)){if(r===rt)return F(a,t),t;const p=Tr(t,e);if(p instanceof P)switch(r){case"hex":case"hexAlpha":return F(a,p),p;default:{if(s)return F(a,p),p;const m=Gr;return F(a,m),m}}else t=p}if(e.format!==r&&(e.format=r),t=t.toLowerCase(),E0.test(t)){const p=Xa(t,e);if(r===ot){let m;return p instanceof P?s?m=p:m=Gr:m=p,F(a,m),m}if(r===rt){let m="";return p instanceof P?m="":m=p,F(a,m),m}p instanceof P?t="":t=p}N0.test(t)&&(t=jr(t,e));let i="",l=NaN,c=NaN,h=NaN,f=NaN;if(t==="transparent")switch(r){case rt:return F(a,t),t;case"hex":return F(a,null),new P;case"hexAlpha":{const p="#00000000";return F(a,p),p}case ot:default:{const p=Gr;return F(a,p),p}}else if(t==="currentcolor"){if(r===rt)return F(a,t),t;if(n){let p;if(n.startsWith(Gn)?p=As(n,e):n.startsWith(ut)?p=Ye(n,e):p=Cn(n,e),p instanceof P)return F(a,p),p;[i,l,c,h,f]=p}else if(r===ot){const p=Gr;return F(a,p),p}}else if(r===rt)if(t.startsWith(Gn)){const p=As(t,e);return F(a,p),p}else if(t.startsWith(ut)){const[p,m,y,b,w]=Ye(t,e);let N="";return w===1?N=`color(${p} ${m} ${y} ${b})`:N=`color(${p} ${m} ${y} ${b} / ${w})`,F(a,N),N}else{const p=Cn(t,e);if(S(p))return F(a,p),p;const[m,y,b,w,N]=p;let E="";return m==="rgb"?N===1?E=`${m}(${y}, ${b}, ${w})`:E=`${m}a(${y}, ${b}, ${w}, ${N})`:N===1?E=`${m}(${y} ${b} ${w})`:E=`${m}(${y} ${b} ${w} / ${N})`,F(a,E),E}else if(t.startsWith(Gn)){/currentcolor/.test(t)&&n&&(t=t.replace(/currentcolor/g,n)),/transparent/.test(t)&&(t=t.replace(/transparent/g,Gr));const p=As(t,e);if(p instanceof P)return F(a,p),p;[i,l,c,h,f]=p}else if(t.startsWith(ut)){const p=Ye(t,e);if(p instanceof P)return F(a,p),p;[i,l,c,h,f]=p}else if(t){const p=Cn(t,e);if(p instanceof P)return F(a,p),p;[i,l,c,h,f]=p}let d="";switch(r){case"hex":{if(Number.isNaN(l)||Number.isNaN(c)||Number.isNaN(h)||Number.isNaN(f)||f===0)return F(a,null),new P;d=ni([l,c,h,1]);break}case"hexAlpha":{if(Number.isNaN(l)||Number.isNaN(c)||Number.isNaN(h)||Number.isNaN(f))return F(a,null),new P;d=ni([l,c,h,f]);break}case ot:default:switch(i){case"rgb":{f===1?d=`${i}(${l}, ${c}, ${h})`:d=`${i}a(${l}, ${c}, ${h}, ${f})`;break}case"lab":case"lch":case"oklab":case"oklch":{f===1?d=`${i}(${l} ${c} ${h})`:d=`${i}(${l} ${c} ${h} / ${f})`;break}default:f===1?d=`color(${i} ${l} ${c} ${h})`:d=`color(${i} ${l} ${c} ${h} / ${f})`}}return F(a,d),d},k0=(t,e={})=>{e.nullable=!1;const n=nr(t,e);return n instanceof P?null:n},{CloseParen:F0,Comma:x0,Comment:S0,EOF:A0,Function:P0,Ident:M0,OpenParen:D0,Whitespace:B0}=v,Li="util",R0=10,Ms=16,rr=360,Ds=180,O0=new RegExp(`^(?:${jn})$`),W0=new RegExp(ws),Ii=(t,e="")=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Li,name:"splitValue",value:t},{delimiter:e}),r=mt(n);if(r instanceof st)return r.item;const s=e===","?/^,$/:/^\s+$/,a=Ve({css:t});let o=0,i="";const l=[];for(;a.length;){const[c,h]=a.shift();switch(c){case x0:{s.test(h)&&o===0?(l.push(i.trim()),i=""):i+=h;break}case S0:break;case P0:case D0:{i+=h,o++;break}case F0:{i+=h,o--;break}case B0:{s.test(h)?o===0?i&&(l.push(i.trim()),i=""):i+=" ":i.endsWith(" ")||(i+=" ");break}default:c===A0?(l.push(i.trim()),i=""):i+=h}}return F(n,l),l},T0=t=>{if(S(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const e=gt({namespace:Li,name:"extractDashedIdent",value:t}),n=mt(e);if(n instanceof st)return n.item;const r=Ve({css:t}),s=new Set;for(;r.length;){const[o,i]=r.shift();o===M0&&i.startsWith("--")&&s.add(i)}const a=[...s];return F(e,a),a},fn=(t,e={})=>{if(S(t)&&(t=t.toLowerCase().trim(),t&&S(t))){if(/^[a-z]+$/.test(t)){if(/^(?:currentcolor|transparent)$/.test(t)||Object.prototype.hasOwnProperty.call(Yn,t))return!0}else if(O0.test(t)||W0.test(t)||(e.nullable=!0,e.format||(e.format=rt),nr(t,e)))return!0}return!1},_i=(t,e=!1)=>typeof t>"u"?"":JSON.stringify(t,(n,r)=>{let s;return typeof r>"u"?s=null:typeof r=="function"?e?s=r.toString().replace(/\s/g,"").substring(0,Ms):s=r.name:r instanceof Map||r instanceof Set?s=[...r]:typeof r=="bigint"?s=r.toString():s=r,s}),B=(t,e=0)=>{if(!Number.isFinite(t))throw new TypeError(`${t} is not a finite number.`);if(Number.isFinite(e)){if(e<0||e>Ms)throw new RangeError(`${e} is not between 0 and ${Ms}.`)}else throw new TypeError(`${e} is not a finite number.`);if(e===0)return Math.round(t);let n;return e===Ms?n=t.toPrecision(6):e<R0?n=t.toPrecision(4):n=t.toPrecision(5),parseFloat(n)},Hi=(t,e,n="shorter")=>{if(!Number.isFinite(t))throw new TypeError(`${t} is not a finite number.`);if(!Number.isFinite(e))throw new TypeError(`${e} is not a finite number.`);switch(n){case"decreasing":{e>t&&(t+=rr);break}case"increasing":{e<t&&(e+=rr);break}case"longer":{e>t&&e<t+Ds?t+=rr:e>t+Ds*-1&&e<=t&&(e+=rr);break}case"shorter":default:e>t+Ds?t+=rr:e<t+Ds*-1&&(e+=rr)}return[t,e]},L0=4096;var Bs,Rs;class st{constructor(e,n=!1){z(this,Bs),z(this,Rs),C(this,Rs,e),C(this,Bs,!!n)}get item(){return u(this,Rs)}get isNull(){return u(this,Bs)}}Bs=new WeakMap,Rs=new WeakMap;class P extends st{constructor(){super(Symbol("null"),!0)}}const sr=new Uc({max:L0}),F=(t,e)=>{t&&(e===null?sr.set(t,new P):e instanceof st?sr.set(t,e):sr.set(t,new st(e)))},mt=t=>{if(t&&sr.has(t)){const e=sr.get(t);return e instanceof st?e:(sr.delete(t),!1)}return!1},gt=(t,e={})=>{const{customProperty:n={},dimension:r={}}=e;let s="";return t&&Object.keys(t).length&&typeof n.callback!="function"&&typeof r.callback!="function"&&(t.opt=_i(e),s=_i(t)),s},{CloseParen:I0,Comment:Ui,Dimension:_0,EOF:H0,Function:U0,OpenParen:z0,Whitespace:zi}=v,Gi="css-calc",G0=3,Za=16,ji=100,j0=new RegExp(gs),q0=new RegExp(Ao),V0=new RegExp(kr),Os=new RegExp(Xc),qi=/\s[*+/-]\s/,Ws=new RegExp(`^(${jt})(${zn}|${Cr})$`),ar=new RegExp(`^(${jt})(${zn}|${Cr}|%)$`),pn=new RegExp(`^(${jt})%$`);var dn,or,ir,Qe,lr,cr,ze,xn,Sn,Je,tn,mn,gn,en,An,Pn;class K0{constructor(){z(this,dn),z(this,or),z(this,ir),z(this,Qe),z(this,lr),z(this,cr),z(this,ze),z(this,xn),z(this,Sn),z(this,Je),z(this,tn),z(this,mn),z(this,gn),z(this,en),z(this,An),z(this,Pn),C(this,dn,!1),C(this,or,[]),C(this,ir,[]),C(this,Qe,!1),C(this,lr,[]),C(this,cr,[]),C(this,ze,!1),C(this,xn,[]),C(this,Sn,[]),C(this,Je,[]),C(this,tn,[]),C(this,mn,!1),C(this,gn,[]),C(this,en,[]),C(this,An,[]),C(this,Pn,[])}get hasNum(){return u(this,dn)}set hasNum(e){C(this,dn,!!e)}get numSum(){return u(this,or)}get numMul(){return u(this,ir)}get hasPct(){return u(this,Qe)}set hasPct(e){C(this,Qe,!!e)}get pctSum(){return u(this,lr)}get pctMul(){return u(this,cr)}get hasDim(){return u(this,ze)}set hasDim(e){C(this,ze,!!e)}get dimSum(){return u(this,xn)}get dimSub(){return u(this,Sn)}get dimMul(){return u(this,Je)}get dimDiv(){return u(this,tn)}get hasEtc(){return u(this,mn)}set hasEtc(e){C(this,mn,!!e)}get etcSum(){return u(this,gn)}get etcSub(){return u(this,en)}get etcMul(){return u(this,An)}get etcDiv(){return u(this,Pn)}clear(){C(this,dn,!1),C(this,or,[]),C(this,ir,[]),C(this,Qe,!1),C(this,lr,[]),C(this,cr,[]),C(this,ze,!1),C(this,xn,[]),C(this,Sn,[]),C(this,Je,[]),C(this,tn,[]),C(this,mn,!1),C(this,gn,[]),C(this,en,[]),C(this,An,[]),C(this,Pn,[])}sort(e=[]){const n=[...e];return n.length>1&&n.sort((r,s)=>{let a;if(ar.test(r)&&ar.test(s)){const[,o,i]=r.match(ar),[,l,c]=s.match(ar);i===c?Number(o)===Number(l)?a=0:Number(o)>Number(l)?a=1:a=-1:i>c?a=1:a=-1}else r===s?a=0:r>s?a=1:a=-1;return a}),n}multiply(){const e=[];let n;if(u(this,dn)){n=1;for(const r of u(this,ir))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;!u(this,Qe)&&!u(this,ze)&&!this.hasEtc&&(Number.isFinite(n)&&(n=B(n,Za)),e.push(n))}if(u(this,Qe)){typeof n!="number"&&(n=1);for(const r of u(this,cr))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;Number.isFinite(n)&&(n=`${B(n,Za)}%`),!u(this,ze)&&!this.hasEtc&&e.push(n)}if(u(this,ze)){let r="",s="",a="";u(this,Je).length&&(u(this,Je).length===1?[s]=u(this,Je):s=`${this.sort(u(this,Je)).join(" * ")}`),u(this,tn).length&&(u(this,tn).length===1?[a]=u(this,tn):a=`${this.sort(u(this,tn)).join(" * ")}`),Number.isFinite(n)?(s?a?a.includes("*")?r=Wt(`calc(${n} * ${s} / (${a}))`,{toCanonicalUnits:!0}):r=Wt(`calc(${n} * ${s} / ${a})`,{toCanonicalUnits:!0}):r=Wt(`calc(${n} * ${s})`,{toCanonicalUnits:!0}):a.includes("*")?r=Wt(`calc(${n} / (${a}))`,{toCanonicalUnits:!0}):r=Wt(`calc(${n} / ${a})`,{toCanonicalUnits:!0}),e.push(r.replace(/^calc/,""))):(!e.length&&n!==void 0&&e.push(n),s?(a?a.includes("*")?r=Wt(`calc(${s} / (${a}))`,{toCanonicalUnits:!0}):r=Wt(`calc(${s} / ${a})`,{toCanonicalUnits:!0}):r=Wt(`calc(${s})`,{toCanonicalUnits:!0}),e.length?e.push("*",r.replace(/^calc/,"")):e.push(r.replace(/^calc/,""))):(r=Wt(`calc(${a})`,{toCanonicalUnits:!0}),e.length?e.push("/",r.replace(/^calc/,"")):e.push("1","/",r.replace(/^calc/,""))))}if(u(this,mn)){if(u(this,An).length){!e.length&&n!==void 0&&e.push(n);const r=this.sort(u(this,An)).join(" * ");e.length?e.push(`* ${r}`):e.push(`${r}`)}if(u(this,Pn).length){const r=this.sort(u(this,Pn)).join(" * ");r.includes("*")?e.length?e.push(`/ (${r})`):e.push(`1 / (${r})`):e.length?e.push(`/ ${r}`):e.push(`1 / ${r}`)}}return e.length?e.join(" "):""}sum(){const e=[];if(u(this,dn)){let n=0;for(const r of u(this,or))if(n+=r,!Number.isFinite(n)||Number.isNaN(n))break;e.push(n)}if(u(this,Qe)){let n=0;for(const r of u(this,lr))if(n+=r,!Number.isFinite(n))break;Number.isFinite(n)&&(n=`${n}%`),e.length?e.push(`+ ${n}`):e.push(n)}if(u(this,ze)){let n,r,s;u(this,xn).length&&(r=u(this,xn).join(" + ")),u(this,Sn).length&&(s=u(this,Sn).join(" + ")),r?s?s.includes("-")?n=Wt(`calc(${r} - (${s}))`,{toCanonicalUnits:!0}):n=Wt(`calc(${r} - ${s})`,{toCanonicalUnits:!0}):n=Wt(`calc(${r})`,{toCanonicalUnits:!0}):n=Wt(`calc(-1 * (${s}))`,{toCanonicalUnits:!0}),e.length?e.push("+",n.replace(/^calc/,"")):e.push(n.replace(/^calc/,""))}if(u(this,mn)){if(u(this,gn).length){const n=this.sort(u(this,gn)).map(r=>{let s;return qi.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?s=`(${r})`:s=r,s}).join(" + ");e.length?u(this,gn).length>1?e.push(`+ (${n})`):e.push(`+ ${n}`):e.push(`${n}`)}if(u(this,en).length){const n=this.sort(u(this,en)).map(r=>{let s;return qi.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?s=`(${r})`:s=r,s}).join(" + ");e.length?u(this,en).length>1?e.push(`- (${n})`):e.push(`- ${n}`):u(this,en).length>1?e.push(`-1 * (${n})`):e.push(`-1 * ${n}`)}}return e.length?e.join(" "):""}}dn=new WeakMap,or=new WeakMap,ir=new WeakMap,Qe=new WeakMap,lr=new WeakMap,cr=new WeakMap,ze=new WeakMap,xn=new WeakMap,Sn=new WeakMap,Je=new WeakMap,tn=new WeakMap,mn=new WeakMap,gn=new WeakMap,en=new WeakMap,An=new WeakMap,Pn=new WeakMap;const Vi=(t=[],e=!1)=>{if(t.length<G0)throw new Error(`Unexpected array length ${t.length}.`);const n=t.shift();if(!S(n)||!n.endsWith("("))throw new Error(`Unexpected token ${n}.`);const r=t.pop();if(r!==")")throw new Error(`Unexpected token ${r}.`);if(t.length===1){const[c]=t;if(!Un(c))throw new Error(`Unexpected token ${c}.`);return`${n}${c}${r}`}const s=[],a=new K0;let o="";const i=t.length;for(let c=0;c<i;c++){const h=t[c];if(!Un(h))throw new Error(`Unexpected token ${h}.`);if(h==="*"||h==="/")o=h;else if(h==="+"||h==="-"){const f=a.multiply();f&&s.push(f,h),a.clear(),o=""}else{const f=Number(h),d=`${h}`;switch(o){case"/":{if(Number.isFinite(f))a.hasNum=!0,a.numMul.push(1/f);else if(pn.test(d)){const[,p]=d.match(pn);a.hasPct=!0,a.pctMul.push(ji*ji/Number(p))}else Ws.test(d)?(a.hasDim=!0,a.dimDiv.push(d)):(a.hasEtc=!0,a.etcDiv.push(d));break}case"*":default:if(Number.isFinite(f))a.hasNum=!0,a.numMul.push(f);else if(pn.test(d)){const[,p]=d.match(pn);a.hasPct=!0,a.pctMul.push(Number(p))}else Ws.test(d)?(a.hasDim=!0,a.dimMul.push(d)):(a.hasEtc=!0,a.etcMul.push(d))}}if(c===i-1){const f=a.multiply();f&&s.push(f),a.clear(),o=""}}let l="";if(e&&(s.includes("+")||s.includes("-"))){const c=[];a.clear(),o="";const h=s.length;for(let f=0;f<h;f++){const d=s[f];if(Un(d))if(d==="+"||d==="-")o=d;else{const p=Number(d),m=`${d}`;switch(o){case"-":{if(Number.isFinite(p))a.hasNum=!0,a.numSum.push(-1*p);else if(pn.test(m)){const[,y]=m.match(pn);a.hasPct=!0,a.pctSum.push(-1*Number(y))}else Ws.test(m)?(a.hasDim=!0,a.dimSub.push(m)):(a.hasEtc=!0,a.etcSub.push(m));break}case"+":default:if(Number.isFinite(p))a.hasNum=!0,a.numSum.push(p);else if(pn.test(m)){const[,y]=m.match(pn);a.hasPct=!0,a.pctSum.push(Number(y))}else Ws.test(m)?(a.hasDim=!0,a.dimSum.push(m)):(a.hasEtc=!0,a.etcSum.push(m))}}if(f===h-1){const p=a.sum();p&&c.push(p),a.clear(),o=""}}l=c.join(" ")}else l=s.join(" ");return`${n}${l}${r}`},X0=(t,e={})=>{const{format:n=""}=e;if(S(t)){if(!Os.test(t)||n!==rt)return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=gt({namespace:Gi,name:"serializeCalc",value:t},e),s=mt(r);if(s instanceof st)return s.item;const a=Ve({css:t}).map(l=>{const[c,h]=l;let f="";return c!==zi&&c!==Ui&&(f=h),f}).filter(l=>l);let o=a.findLastIndex(l=>/\($/.test(l));for(;o;){const l=a.findIndex((f,d)=>f===")"&&d>o),c=a.slice(o,l+1);let h=Vi(c);Os.test(h)&&(h=Wt(h,{toCanonicalUnits:!0})),a.splice(o,l-o+1,h),o=a.findLastIndex(f=>/\($/.test(f))}const i=Vi(a,!0);return F(r,i),i},Ki=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const[,,,,n={}]=t,{unit:r,value:s}=n,{dimension:a={}}=e;if(r==="px")return`${s}${r}`;const o=Number(s);if(r&&Number.isFinite(o)){let i;if(Object.hasOwnProperty.call(a,r)?i=a[r]:typeof a.callback=="function"&&(i=a.callback(r)),i=Number(i),Number.isFinite(i))return`${o*i}px`}return new P},Z0=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{format:n=""}=e,r=new Set;let s=0;const a=[];for(;t.length;){const o=t.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i="",l=""]=o;switch(i){case _0:{if(n===rt&&!r.has(s))a.push(l);else{const c=Ki(o,e);S(c)?a.push(c):a.push(l)}break}case U0:case z0:{a.push(l),s++,q0.test(l)&&r.add(s);break}case I0:{a.length&&a[a.length-1]===" "?a.splice(-1,1,l):a.push(l),r.has(s)&&r.delete(s),s--;break}case zi:{if(a.length){const c=a[a.length-1];S(c)&&!c.endsWith("(")&&c!==" "&&a.push(l)}break}default:i!==Ui&&i!==H0&&a.push(l)}}return a},jr=(t,e={})=>{const{format:n=""}=e;if(S(t)){if(V0.test(t)){if(n===rt)return t;{const l=Tr(t,e);return S(l)?l:""}}else if(!j0.test(t))return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=gt({namespace:Gi,name:"cssCalc",value:t},e),s=mt(r);if(s instanceof st)return s.item;const a=Ve({css:t}),o=Z0(a,e);let i=Wt(o.join(""),{toCanonicalUnits:!0});if(Os.test(t)){if(ar.test(i)){const[,l,c]=i.match(ar);i=`${B(Number(l),Za)}${c}`}i&&!Os.test(i)&&n===rt&&(i=`calc(${i})`)}return F(r,i),i},Y0="css-gradient",Ts=`${jt}(?:${zn})`,Xi=`${Ts}|${Lt}`,Q0=`${jt}(?:${Cr})|0`,It=`${Q0}|${Lt}`,Zi=`${So}(?:${Cr}|%)|0`,J0=`${So}(?:${Cr})|0`,Jt="center",Ya="left|right",Qa="top|bottom",vn="start|end",ur=`${Ya}|x-(?:${vn})`,hr=`${Qa}|y-(?:${vn})`,qr=`block-(?:${vn})`,Vr=`inline-(?:${vn})`,t1=`${Jt}|${ur}|${hr}|${qr}|${Vr}|${It}`,e1=[`(?:${Jt}|${ur})\\s+(?:${Jt}|${hr})`,`(?:${Jt}|${hr})\\s+(?:${Jt}|${ur})`,`(?:${Jt}|${ur}|${It})\\s+(?:${Jt}|${hr}|${It})`,`(?:${Jt}|${qr})\\s+(?:${Jt}|${Vr})`,`(?:${Jt}|${Vr})\\s+(?:${Jt}|${qr})`,`(?:${Jt}|${vn})\\s+(?:${Jt}|${vn})`].join("|"),n1=[`(?:${ur})\\s+(?:${It})\\s+(?:${hr})\\s+(?:${It})`,`(?:${hr})\\s+(?:${It})\\s+(?:${ur})\\s+(?:${It})`,`(?:${qr})\\s+(?:${It})\\s+(?:${Vr})\\s+(?:${It})`,`(?:${Vr})\\s+(?:${It})\\s+(?:${qr})\\s+(?:${It})`,`(?:${vn})\\s+(?:${It})\\s+(?:${vn})\\s+(?:${It})`].join("|"),Yi="(?:clos|farth)est-(?:corner|side)",Ls=[`${Yi}(?:\\s+${Yi})?`,`${J0}`,`(?:${Zi})\\s+(?:${Zi})`].join("|"),Is="circle|ellipse",Qi=`from\\s+${Ts}`,nn=`at\\s+(?:${t1}|${e1}|${n1})`,Ji=`to\\s+(?:(?:${Ya})(?:\\s(?:${Qa}))?|(?:${Qa})(?:\\s(?:${Ya}))?)`,Ce=`in\\s+(?:${To}|${Oo})`,tl=/^(?:repeating-)?(?:conic|linear|radial)-gradient\(/,r1=/^((?:repeating-)?(?:conic|linear|radial)-gradient)\(/,s1=t=>{if(S(t)&&(t=t.trim(),tl.test(t))){const[,e]=t.match(r1);return e}return""},a1=(t,e)=>{if(S(t)&&S(e)){t=t.trim(),e=e.trim();let n="";if(/^(?:repeating-)?linear-gradient$/.test(e)?n=[`(?:${Ts}|${Ji})(?:\\s+${Ce})?`,`${Ce}(?:\\s+(?:${Ts}|${Ji}))?`].join("|"):/^(?:repeating-)?radial-gradient$/.test(e)?n=[`(?:${Is})(?:\\s+(?:${Ls}))?(?:\\s+${nn})?(?:\\s+${Ce})?`,`(?:${Ls})(?:\\s+(?:${Is}))?(?:\\s+${nn})?(?:\\s+${Ce})?`,`${nn}(?:\\s+${Ce})?`,`${Ce}(?:\\s+${Is})(?:\\s+(?:${Ls}))?(?:\\s+${nn})?`,`${Ce}(?:\\s+${Ls})(?:\\s+(?:${Is}))?(?:\\s+${nn})?`,`${Ce}(?:\\s+${nn})?`].join("|"):/^(?:repeating-)?conic-gradient$/.test(e)&&(n=[`${Qi}(?:\\s+${nn})?(?:\\s+${Ce})?`,`${nn}(?:\\s+${Ce})?`,`${Ce}(?:\\s+${Qi})?(?:\\s+${nn})?`].join("|")),n)return new RegExp(`^(?:${n})$`).test(t)}return!1},el=(t,e,n={})=>{if(Array.isArray(t)&&t.length>1){const r=/^(?:repeating-)?conic-gradient$/.test(e)?Xi:It,s=new RegExp(`^(?:${r})$`),a=new RegExp(`(?:\\s+(?:${r})){1,2}$`),o=[];for(const l of t)if(S(l))if(s.test(l))o.push("hint");else{const c=l.replace(a,"");if(fn(c,n))o.push("color");else return!1}const i=o.join(",");return/^color(?:,(?:hint,)?color)+$/.test(i)}return!1},o1=(t,e={})=>{if(S(t)){t=t.trim();const n=gt({namespace:Y0,name:"parseGradient",value:t},e),r=mt(n);if(r instanceof st)return r.isNull?null:r.item;const s=s1(t),a=t.replace(tl,"").replace(/\)$/,"");if(s&&a){const[o="",...i]=Ii(a,","),l=/^(?:repeating-)?conic-gradient$/.test(s)?Xi:It,c=new RegExp(`(?:\\s+(?:${l})){1,2}$`);let h=!1;if(c.test(o)){const f=o.replace(c,"");fn(f,e)&&(h=!0)}else fn(o,e)&&(h=!0);if(h){if(i.unshift(o),el(i,s,e)){const f={value:t,type:s,colorStopList:i};return F(n,f),f}}else if(i.length>1){const f=o;if(a1(f,s)&&el(i,s,e)){const d={value:t,type:s,gradientLine:f,colorStopList:i};return F(n,d),d}}}return F(n,null),null}return null},i1=(t,e={})=>o1(t,e)!==null,Ge="convert",l1=new RegExp(gs),c1=new RegExp(Io),u1=new RegExp(kr),rn=(t,e={})=>{if(S(t)){if(t=t.trim(),!t)return new P}else return new P;const n=gt({namespace:Ge,name:"preProcess",value:t},e),r=mt(n);if(r instanceof st)return r.isNull?r:r.item;if(u1.test(t)){const s=Tr(t,e);if(S(s))t=s;else return F(n,null),new P}if(c1.test(t)){const s=Xa(t,e);if(S(s))t=s;else return F(n,null),new P}else l1.test(t)&&(t=jr(t,e));if(t.startsWith("color-mix")){const s=structuredClone(e);s.format=ot,s.nullable=!0;const a=nr(t,s);return F(n,a),a}return F(n,t),t},h1=t=>Rr(t),f1=(t,e={})=>{if(S(t)){const o=rn(t,e);if(o instanceof P)return null;t=o.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const{alpha:n=!1}=e,r=gt({namespace:Ge,name:"colorToHex",value:t},e),s=mt(r);if(s instanceof st)return s.isNull?null:s.item;let a;return e.nullable=!0,n?(e.format="hexAlpha",a=nr(t,e)):(e.format="hex",a=nr(t,e)),S(a)?(F(r,a),a):(F(r,null),null)},p1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Ge,name:"colorToHsl",value:t},e),r=mt(n);if(r instanceof st)return r.item;e.format="hsl";const s=ka(t,e);return F(n,s),s},d1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Ge,name:"colorToHwb",value:t},e),r=mt(n);if(r instanceof st)return r.item;e.format="hwb";const s=Fa(t,e);return F(n,s),s},m1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Ge,name:"colorToLab",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=xa(t,e);return F(n,s),s},g1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Ge,name:"colorToLch",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Sa(t,e);return F(n,s),s},v1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Ge,name:"colorToOklab",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Aa(t,e);return F(n,s),s},b1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Ge,name:"colorToOklch",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Pa(t,e);return F(n,s),s},w1=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Ge,name:"colorToRgb",value:t},e),r=mt(n);if(r instanceof st)return r.item;const s=Wr(t,e);return F(n,s),s},nl=(t,e={})=>{if(S(t)){const a=rn(t,e);if(a instanceof P)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=gt({namespace:Ge,name:"colorToXyz",value:t},e),r=mt(n);if(r instanceof st)return r.item;let s;return t.startsWith("color(")?[,...s]=bt(t,e):[,...s]=Nt(t,e),F(n,s),s},$1=(t,e={})=>(e.d50=!0,nl(t,e)),y1={colorToHex:f1,colorToHsl:p1,colorToHwb:d1,colorToLab:m1,colorToLch:g1,colorToOklab:v1,colorToOklch:b1,colorToRgb:w1,colorToXyz:nl,colorToXyzD50:$1,numberToHex:h1};/*!
 * CSS color - Resolve, parse, convert CSS color.
 * @license MIT
 * @copyright asamuzaK (Kazz)
 * @see {@link https://github.com/asamuzaK/cssColor/blob/main/LICENSE}
 */const Ja={cssCalc:jr,cssVar:Eu,extractDashedIdent:T0,isColor:fn,isGradient:i1,splitValue:Ii},N1=Ja.isColor,E1=Ja.cssCalc;export{y1 as convert,E1 as cssCalc,N1 as isColor,k0 as resolve,Ja as utils};
