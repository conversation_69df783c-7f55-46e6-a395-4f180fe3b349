{"name": "@asamuzakjp/css-color", "description": "CSS color - Resolve and convert CSS colors.", "author": "asamuzaK", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/asamuzaK/cssColor.git"}, "homepage": "https://github.com/asamuzaK/cssColor#readme", "bugs": {"url": "https://github.com/asamuzaK/cssColor/issues"}, "files": ["dist", "src"], "type": "module", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "main": "dist/cjs/index.cjs", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "packageManager": "pnpm@10.6.1", "dependencies": {"@csstools/css-calc": "^2.1.2", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4", "@csstools/css-tokenizer": "^3.0.3", "lru-cache": "^10.4.3"}, "devDependencies": {"@tanstack/vite-config": "^0.1.0", "@vitest/coverage-istanbul": "^3.0.8", "esbuild": "^0.25.0", "eslint": "^9.22.0", "eslint-plugin-import-x": "^4.6.1", "eslint-plugin-regexp": "^2.7.0", "globals": "^16.0.0", "knip": "^5.45.0", "neostandard": "^0.12.1", "prettier": "^3.5.3", "publint": "^0.3.8", "rimraf": "^6.0.1", "tsup": "^8.4.0", "typescript": "^5.8.2", "vite": "^6.2.1", "vitest": "^3.0.8"}, "scripts": {"build": "pnpm run clean && pnpm run test && pnpm run knip && pnpm run build:prod && pnpm run build:cjs && pnpm run build:browser && pnpm run publint", "build:browser": "vite build -c ./vite.browser.config.ts", "build:prod": "vite build", "build:cjs": "tsup ./src/index.ts --format=cjs --platform=node --outDir=./dist/cjs/ --sourcemap --dts", "clean": "rimraf ./coverage ./dist", "knip": "knip", "prettier": "prettier . --ignore-unknown --write", "publint": "publint --strict", "test": "pnpm run prettier && pnpm run --stream \"/^test:.*/\"", "test:eslint": "eslint ./src ./test --fix", "test:types": "tsc", "test:unit": "vitest"}, "version": "3.1.1"}