{"safari": ["<PERSON><PERSON><PERSON>", "Origin", "Content-Type", "Accept", "Upgrade-Insecure-Requests", "User-Agent", "Content-Length", "Accept-Encoding", "Accept-Language", "Connection", "Host", "<PERSON><PERSON>", "Sec-Fetch-Dest", "Sec-Fetch-Mode", "Sec-Fetch-Site", ":method", ":scheme", ":authority", ":path", "referer", "origin", "content-type", "accept", "user-agent", "content-length", "accept-encoding", "accept-language", "cookie", "sec-fetch-dest", "sec-fetch-mode", "sec-fetch-site"], "chrome": ["Host", "Connection", "Content-Length", "Cache-Control", "sec-ch-ua", "sec-ch-ua-mobile", "sec-ch-ua-platform", "Upgrade-Insecure-Requests", "Origin", "Content-Type", "User-Agent", "Accept", "Sec-Fetch-Site", "Sec-Fetch-Mode", "Sec-Fetch-User", "Sec-Fetch-Dest", "<PERSON><PERSON><PERSON>", "Accept-Encoding", "Accept-Language", "<PERSON><PERSON>", ":method", ":authority", ":scheme", ":path", "content-length", "cache-control", "sec-ch-ua", "sec-ch-ua-mobile", "sec-ch-ua-platform", "upgrade-insecure-requests", "origin", "content-type", "user-agent", "accept", "sec-fetch-site", "sec-fetch-mode", "sec-fetch-user", "sec-fetch-dest", "referer", "accept-encoding", "accept-language", "cookie"], "firefox": ["Host", "User-Agent", "Accept", "Accept-Language", "Accept-Encoding", "Content-Type", "Content-Length", "Origin", "Connection", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Upgrade-Insecure-Requests", "Sec-Fetch-Dest", "Sec-Fetch-Mode", "Sec-Fetch-Site", "Sec-Fetch-User", ":method", ":path", ":authority", ":scheme", "user-agent", "accept", "accept-language", "accept-encoding", "content-type", "content-length", "origin", "referer", "cookie", "upgrade-insecure-requests", "sec-fetch-dest", "sec-fetch-mode", "sec-fetch-site", "sec-fetch-user", "te"], "edge": []}