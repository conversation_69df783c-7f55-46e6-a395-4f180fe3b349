import Pop3Command from '../src/Command.js';
import {readFileSync} from 'fs';

const config = JSON.parse(readFileSync(new URL('../pop.config.json', import.meta.url)));
const pop3 = new Pop3Command(config);

// These must be in order:
await pop3.connect();
await pop3.command('USER', config.user);
await pop3.command('PASS', config.password);

const [statInfo] = await pop3.command('STAT');
const [retrInfo, retrStream] = await pop3.command('RETR', 1);

console.log(statInfo); // 100 102400
console.log(retrInfo); // 1024 octets

const [quitInfo] = await pop3.command('QUIT');
console.log(quitInfo);

// console.log(await Pop3Command.stream2Stringstream2String(statStream));
// console.log(await Pop3Command.stream2String(retrStream));
console.log(await Pop3Command.listify(await Pop3Command.stream2String(retrStream)));
