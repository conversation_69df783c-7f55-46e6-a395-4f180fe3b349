import { TypedArray } from '../typed-array.js';
import { Predicate } from './predicate';
export declare class TypedArrayPredicate<T extends TypedArray> extends Predicate<T> {
    /**
    Test a typed array to have a specific byte length.

    @param byteLength - The byte length of the typed array.
    */
    byteLength(byteLength: number): this;
    /**
    Test a typed array to have a minimum byte length.

    @param byteLength - The minimum byte length of the typed array.
    */
    minByteLength(byteLength: number): this;
    /**
    Test a typed array to have a minimum byte length.

    @param length - The minimum byte length of the typed array.
    */
    maxByteLength(byteLength: number): this;
    /**
    Test a typed array to have a specific length.

    @param length - The length of the typed array.
    */
    length(length: number): this;
    /**
    Test a typed array to have a minimum length.

    @param length - The minimum length of the typed array.
    */
    minLength(length: number): this;
    /**
    Test a typed array to have a maximum length.

    @param length - The maximum length of the typed array.
    */
    maxLength(length: number): this;
}
