{"version": 3, "file": "constant.cjs", "names": ["CRLF", "exports", "CRLF_BUFFER", "<PERSON><PERSON><PERSON>", "from", "TERMINATOR_BUFFER", "TERMINATOR_BUFFER_ARRAY", "MULTI_LINE_COMMAND_NAME", "MAYBE_MULTI_LINE_COMMAND_NAME"], "sources": ["../src/constant.js"], "sourcesContent": ["export const CRLF = '\\r\\n';\nexport const CRLF_BUFFER = Buffer.from('\\r\\n');\nexport const TERMINATOR_BUFFER = Buffer.from('\\r\\n.\\r\\n');\nexport const TERMINATOR_BUFFER_ARRAY = [\n  Buffer.from('\\r\\n.\\r\\n'),\n  Buffer.from('.\\r\\n'),\n  Buffer.from('.')\n];\n\nexport const MULTI_LINE_COMMAND_NAME = [\n  'RETR',\n  'TOP'\n];\nexport const MAYBE_MULTI_LINE_COMMAND_NAME = [\n  'LIST',\n  'UIDL'\n];\n"], "mappings": ";;;;;;AAAO,MAAMA,IAAI,GAAG,MAAM;AAACC,OAAA,CAAAD,IAAA,GAAAA,IAAA;AACpB,MAAME,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,MAAM,CAAC;AAACH,OAAA,CAAAC,WAAA,GAAAA,WAAA;AACxC,MAAMG,iBAAiB,GAAGF,MAAM,CAACC,IAAI,CAAC,WAAW,CAAC;AAACH,OAAA,CAAAI,iBAAA,GAAAA,iBAAA;AACnD,MAAMC,uBAAuB,GAAG,CACrCH,MAAM,CAACC,IAAI,CAAC,WAAW,CAAC,EACxBD,MAAM,CAACC,IAAI,CAAC,OAAO,CAAC,EACpBD,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC,CACjB;AAACH,OAAA,CAAAK,uBAAA,GAAAA,uBAAA;AAEK,MAAMC,uBAAuB,GAAG,CACrC,MAAM,EACN,KAAK,CACN;AAACN,OAAA,CAAAM,uBAAA,GAAAA,uBAAA;AACK,MAAMC,6BAA6B,GAAG,CAC3C,MAAM,EACN,MAAM,CACP;AAACP,OAAA,CAAAO,6BAAA,GAAAA,6BAAA"}