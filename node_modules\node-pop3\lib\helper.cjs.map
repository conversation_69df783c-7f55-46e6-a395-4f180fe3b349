{"version": 3, "file": "helper.cjs", "names": ["_constant", "require", "stream2String", "stream", "Promise", "resolve", "reject", "buffer", "<PERSON><PERSON><PERSON>", "concat", "length", "len", "on", "_buffer", "err", "toString", "listify", "str", "split", "CRLF", "filter", "Boolean", "map", "line"], "sources": ["../src/helper.js"], "sourcesContent": ["import {CRLF} from './constant.js';\n\n/**\n * @param {import('stream').Stream} stream\n */\nexport function stream2String (stream) {\n  // eslint-disable-next-line promise/avoid-new -- Our own API\n  return new Promise((resolve, reject) => {\n    let buffer = Buffer.concat([]);\n    let {length: len} = buffer;\n    stream.on('data', (_buffer) => {\n      len += _buffer.length;\n      buffer = Buffer.concat([buffer, _buffer], len);\n    });\n    stream.on('error', (err) => reject(err));\n    stream.on('end', () => resolve(buffer.toString()));\n  });\n}\n\n/**\n * @param {string} str\n * @returns {string[][]}\n */\nexport function listify (str) {\n  return str.split(CRLF)\n    .filter(Boolean)\n    .map((line) => line.split(' '));\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAEA;AACA;AACA;AACO,SAASC,aAAaA,CAAEC,MAAM,EAAE;EACrC;EACA,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAIC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,CAAC;IAC9B,IAAI;MAACC,MAAM,EAAEC;IAAG,CAAC,GAAGJ,MAAM;IAC1BJ,MAAM,CAACS,EAAE,CAAC,MAAM,EAAGC,OAAO,IAAK;MAC7BF,GAAG,IAAIE,OAAO,CAACH,MAAM;MACrBH,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,CAACF,MAAM,EAAEM,OAAO,CAAC,EAAEF,GAAG,CAAC;IAChD,CAAC,CAAC;IACFR,MAAM,CAACS,EAAE,CAAC,OAAO,EAAGE,GAAG,IAAKR,MAAM,CAACQ,GAAG,CAAC,CAAC;IACxCX,MAAM,CAACS,EAAE,CAAC,KAAK,EAAE,MAAMP,OAAO,CAACE,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACO,SAASC,OAAOA,CAAEC,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACC,KAAK,CAACC,cAAI,CAAC,CACnBC,MAAM,CAACC,OAAO,CAAC,CACfC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACL,KAAK,CAAC,GAAG,CAAC,CAAC;AACnC"}