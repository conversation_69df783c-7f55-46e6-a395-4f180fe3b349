{"name": "fingerprint-generator", "version": "2.1.62", "description": "NodeJS package for generating realistic browser fingerprints.", "homepage": "https://github.com/apify/fingerprint-suite", "engines": {"node": ">=16.0.0"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}}, "scripts": {"build": "npm run clean && npm run compile", "postbuild": "cp -r src/data_files  && cp ../../README.md ", "clean": "rimraf ./dist", "compile": "tsc -p tsconfig.build.json && gen-esm-wrapper ./index.js ./index.mjs", "copy": "ts-node -T ../../scripts/copy.ts"}, "repository": {"type": "git", "url": "git+https://github.com/apify/fingerprint-suite.git"}, "author": "Apify", "license": "Apache-2.0", "bugs": {"url": "https://github.com/apify/fingerprint-suite/issues"}, "dependencies": {"generative-bayesian-network": "^2.1.62", "header-generator": "^2.1.62", "tslib": "^2.4.0"}}