export declare const SUPPORTED_BROWSERS: readonly ["chrome", "firefox", "safari", "edge"];
export declare const SUPPORTED_OPERATING_SYSTEMS: readonly ["windows", "macos", "linux", "android", "ios"];
export declare const SUPPORTED_DEVICES: readonly ["desktop", "mobile"];
export declare const SUPPORTED_HTTP_VERSIONS: readonly ["1", "2"];
export declare const BROWSER_HTTP_NODE_NAME: "*BROWSER_HTTP";
export declare const OPERATING_SYSTEM_NODE_NAME: "*OPERATING_SYSTEM";
export declare const DEVICE_NODE_NAME: "*DEVICE";
export declare const MISSING_VALUE_DATASET_TOKEN: "*MISSING_VALUE*";
export declare const HTTP1_SEC_FETCH_ATTRIBUTES: {
    readonly mode: "Sec-Fetch-Mode";
    readonly dest: "Sec-<PERSON>tch-Dest";
    readonly site: "Sec-Fetch-Site";
    readonly user: "Sec-Fetch-User";
};
export declare const HTTP2_SEC_FETCH_ATTRIBUTES: {
    readonly mode: "sec-fetch-mode";
    readonly dest: "sec-fetch-dest";
    readonly site: "sec-fetch-site";
    readonly user: "sec-fetch-user";
};
//# sourceMappingURL=constants.d.ts.map