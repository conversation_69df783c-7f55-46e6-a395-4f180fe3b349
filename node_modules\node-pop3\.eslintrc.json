{"extends": "ash-nazg/sauron-overrides", "parserOptions": {"sourceType": "module", "ecmaVersion": 2022}, "settings": {"polyfills": ["Promise", "URL"]}, "env": {"es6": true, "node": true}, "overrides": [{"files": "*.md/*.js", "globals": {"require": true, "pop3": true, "Pop3Command": true}, "rules": {"import/unambiguous": "off", "import/no-unresolved": "off", "import/no-commonjs": "off", "no-console": "off", "no-shadow": ["error", {"allow": ["Pop3Command"]}], "no-unused-vars": ["error", {"varsIgnorePattern": "Pop3Command|str"}]}}, {"files": ["test/**"], "env": {"mocha": true}, "globals": {"expect": true}}], "rules": {}}