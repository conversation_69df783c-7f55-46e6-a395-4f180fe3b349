{"version": 3, "file": "fingerprint-generator.js", "sourceRoot": "", "sources": ["../src/fingerprint-generator.ts"], "names": [], "mappings": ";;;AAAA,6EAAqE;AACrE,uDAAoF;AAEpF,2CAA8E;AA2F9E;;GAEG;AACH,MAAa,oBAAqB,SAAQ,kCAAe;IAIrD;;OAEG;IACH,YAAY,UAAgD,EAAE;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAC;QAPnB;;;;;WAAiC;QACjC;;;;;WAAmG;QAO/F,IAAI,CAAC,wBAAwB,GAAG;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,IAAI,EAAE,OAAO,CAAC,IAAI;SACrB,CAAC;QACF,IAAI,CAAC,2BAA2B,GAAG,IAAI,6CAAe,CAAC,EAAE,IAAI,EAAE,GAAG,SAAS,gDAAgD,EAAE,CAAC,CAAC;IACnI,CAAC;IAED;;;;;OAKG;IACH,cAAc,CACV,UAAgD,EAAE,EAClD,0BAAmC,EAAE;QAErC,MAAM,cAAc,GAA6B,EAAE,CAAC;QAEpD,OAAO,GAAG;YACN,GAAG,IAAI,CAAC,wBAAwB;YAChC,GAAG,OAAO;SACb,CAAC;QAEF,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE;YACrB,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;YACnF,MAAM,6BAA6B,GAAG,eAAe,CAAC;YAEtD,IAAI,CAAC,6BAA6B;gBAAE,OAAO,SAAS,CAAC;YAErD,cAAc,CAAC,MAAM,GAAG,eAAe;gBACnC,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,YAAoB,EAAE,EAAE;oBACjG,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,8BAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrE,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,CAAC;2BAC5D,MAAM,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,GAAG,CAAC;2BACjD,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;2BACjD,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC;gBACpD,CAAC,CAAC;gBACF,CAAC,CAAC,SAAS,CAAC;YAEhB,IAAI;gBACA,OAAO,mCAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;aACvF;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,OAAO,EAAE,MAAM;oBAAE,MAAM,CAAC,CAAC;gBAC7B,OAAO,cAAc,CAAC,MAAM,CAAC;gBAC7B,OAAO,SAAS,CAAC;aACpB;QACL,CAAC,CAAC,EAAE,CAAC;QAEL,KAAK,IAAI,eAAe,GAAG,CAAC,EAAE,eAAe,GAAG,EAAE,EAAE,eAAe,EAAE,EAAE;YACnE,0HAA0H;YAC1H,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,uBAAuB,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAC1F,MAAM,SAAS,GAAG,YAAY,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAE1F,gEAAgE;YAChE,MAAM,WAAW,GAAwB,IAAI,CAAC,2BAA2B,CAAC,oCAAoC,CAAC;gBAC3G,GAAG,cAAc;gBACjB,SAAS,EAAE,CAAC,SAAS,CAAC;aACzB,CAAC,CAAC;YAEH;;eAEG;YACH,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC9C,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,uCAA2B,EAAE;oBACxD,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;iBACjC;qBAAM,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,8BAAkB,CAAC,EAAE;oBAC9D,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,8BAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;iBAChG;aACJ;YAED,IAAI,CAAC,WAAW,CAAC,MAAM;gBAAE,SAAS,CAAC,wFAAwF;YAE3H,mEAAmE;YACnE,MAAM,yBAAyB,GAAG,iBAAiB,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACzH,MAAM,iBAAiB,GAAG,EAAE,CAAC;YAC7B,KAAK,MAAM,MAAM,IAAI,yBAAyB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBACvD,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAChD;YACD,WAAW,CAAC,SAAS,GAAG,iBAAiB,CAAC;YAE1C,OAAO;gBACH,WAAW,EAAE;oBACT,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC;oBACzC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,wBAAwB,CAAC,UAAU,IAAI,KAAK;oBACnF,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,IAAI,KAAK;iBACpE;gBACD,OAAO;aACV,CAAC;SACL;QAED,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;IACrF,CAAC;IAED;;;;;OAKG;IACK,oBAAoB,CAAC,WAAgC;QACzD,MAAM,EACF,SAAS,EACT,aAAa,EACb,UAAU,EACV,WAAW,EACX,OAAO,EACP,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,mBAAmB,EACnB,OAAO,EACP,UAAU,EACV,MAAM,EACN,SAAS,EACT,cAAc,EACd,eAAe,EACf,MAAM,EACN,WAAW,EACX,WAAW,EACX,WAAW,EACX,OAAO,EACP,SAAS,EACT,iBAAiB,EACjB,KAAK,GACR,GAAG,WAAW,CAAC;QAChB,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAChD,MAAM,iBAAiB,GAAG,QAAQ,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QAEvD,MAAM,SAAS,GAAG;YACd,SAAS;YACT,aAAa;YACb,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;YACtB,SAAS;YACT,QAAQ;YACR,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;YAC9D,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB,EAAE,EAAE,CAAC;YACtD,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;YACvE,OAAO;YACP,UAAU;YACV,MAAM;YACN,SAAS;YACT,UAAU;YACV,WAAW;YACX,OAAO;YACP,UAAU;YACV,KAAK;YACL,eAAe;YACf,SAAS;SACZ,CAAC;QAEF,OAAO;YACH,MAAM;YACN,SAAS;YACT,WAAW;YACX,WAAW;YACX,WAAW;YACX,OAAO;YACP,SAAS;YACT,iBAAiB;YACjB,KAAK;SACO,CAAC;IACrB,CAAC;CACJ;AAhLD,oDAgLC"}